import uuid
from datetime import datetime, timezone
from typing import Any, Dict, Literal, Optional

from pydantic import BaseModel, Field, field_validator, model_validator


class ParticipantInfo(BaseModel):
    """Information about the sender of a message."""

    participant_type: Literal["user", "ai"] = Field(
        description="The type of participant in the conversation."
    )
    participant_id: uuid.UUID = Field(
        description="The unique identifier for the participant."
    )

    class Config:
        frozen = True
        str_strip_whitespace = True


class TextMessageContent(BaseModel):
    """Content structure for text-based messages."""

    text: str = Field(
        min_length=1, max_length=10000, description="The text content of the message."
    )

    @field_validator("text")
    @classmethod
    def validate_text_not_empty(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Text content cannot be empty or whitespace only")
        return v.strip()

    class Config:
        frozen = True
        str_strip_whitespace = True


class AudioMessageContent(BaseModel):
    """Content structure for audio messages."""

    audio_url: str = Field(description="The URL or file path to the audio file.")
    duration_seconds: Optional[int] = Field(
        None, ge=0, le=3600, description="The duration of the audio file in seconds."
    )
    text: Optional[str] = Field(
        None,
        max_length=10000,
        description="The transcribed text content of the audio message.",
    )

    @field_validator("audio_url")
    @classmethod
    def validate_audio_url(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Audio URL cannot be empty")
        return v.strip()

    class Config:
        frozen = True
        str_strip_whitespace = True


class ImageMessageContent(BaseModel):
    """Content structure for image messages."""

    image_url: str = Field(description="The URL or file path to the image file.")
    caption: Optional[str] = Field(
        None, max_length=1000, description="An optional caption describing the image."
    )

    @field_validator("image_url")
    @classmethod
    def validate_image_url(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Image URL cannot be empty")
        return v.strip()

    class Config:
        frozen = True
        str_strip_whitespace = True


class ChatMessage(BaseModel):
    """
    A single message within a chat conversation.

    This model represents the structure of a message that can contain
    different types of content including text, audio, or images.
    """

    message_id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        description="The unique identifier for this message.",
        alias="id",
    )
    chat_id: uuid.UUID = Field(
        description="The identifier of the chat this message belongs to."
    )
    sender: ParticipantInfo = Field(
        description="Information about who sent this message."
    )
    content_type: Literal["text", "audio", "image"] = Field(
        description="The type of content contained in this message."
    )
    text_content: Optional[TextMessageContent] = Field(
        None, description="The text content when message type is text."
    )
    audio_content: Optional[AudioMessageContent] = Field(
        None, description="The audio content when message type is audio."
    )
    image_content: Optional[ImageMessageContent] = Field(
        None, description="The image content when message type is image."
    )
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this message was created.",
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata such as reactions or read status."
    )

    @model_validator(mode="after")
    def validate_content_consistency(self) -> "ChatMessage":
        """Ensure content fields match the specified content type."""
        content_fields = {
            "text": self.text_content,
            "audio": self.audio_content,
            "image": self.image_content,
        }

        # Verify the correct content field is populated
        expected_content = content_fields[self.content_type]
        if expected_content is None:
            raise ValueError(
                f"Content field for type '{self.content_type}' must be provided"
            )

        # Verify no other content fields are populated
        for content_type, content in content_fields.items():
            if content_type != self.content_type and content is not None:
                raise ValueError(
                    f"Only {self.content_type}_content should be provided for content_type '{self.content_type}'"
                )

        return self

    class Config:
        use_enum_values = True
        validate_assignment = True
        str_strip_whitespace = True


class Chat(BaseModel):
    """
    A conversation chat within a specific world.
    """

    chat_id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        description="The unique identifier for this chat.",
        alias="id",
    )
    world_id: uuid.UUID = Field(
        description="The identifier of the world this chat belongs to."
    )
    chat_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="An optional custom name for this chat.",
    )
    last_message_summary: Optional[str] = Field(
        None, max_length=500, description="A brief summary of the most recent message."
    )
    last_message_timestamp: Optional[datetime] = Field(
        None, description="When the most recent message was sent."
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this chat was created.",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this chat was last modified.",
    )

    @field_validator("chat_name")
    @classmethod
    def validate_chat_name(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and not v.strip():
            raise ValueError("Chat name cannot be empty or whitespace only")
        return v.strip() if v else None

    @model_validator(mode="after")
    def validate_timestamps(self) -> "Chat":
        """Ensure timestamp consistency."""
        if (
            self.last_message_timestamp is not None
            and self.last_message_timestamp < self.created_at
        ):
            raise ValueError("Last message timestamp cannot be before chat creation")

        if self.updated_at < self.created_at:
            raise ValueError("Updated timestamp cannot be before creation timestamp")

        return self

    class Config:
        validate_assignment = True
        str_strip_whitespace = True
