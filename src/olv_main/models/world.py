from typing import Optional, List, Union, Literal, Self
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from datetime import datetime, timezone
from enum import Enum
import uuid
import re

from .lorebook import LorebookInWorld


# Enum for world visibility
class WorldVisibilityEnum(str, Enum):
    PRIVATE = "private"
    UNLISTED = "unlisted"
    PUBLIC = "public"


# Pydantic model for an individual character's configuration within a World
class CharacterInWorld(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True,
        str_strip_whitespace=True,
        validate_default=True,
        frozen=False,
    )

    character_id: str = Field(
        ...,
        alias="characterID",
        description="The unique identifier of the AI character.",
        min_length=1,
    )
    affect: bool = Field(
        ...,
        description="Whether interactions in this world influence the character's long-term memory.",
    )
    role_prompt: Optional[str] = Field(
        None,
        alias="rolePrompt",
        description="Custom role or prompt adjustment for the character in this world context.",
        max_length=2000,
    )

    @field_validator("character_id")
    @classmethod
    def validate_character_id(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Character ID cannot be empty")
        return v.strip()


# World Pydantic model
class World(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True,
        use_enum_values=True,
        str_strip_whitespace=True,
        validate_default=True,
        frozen=False,
    )

    id_: str = Field(
        ...,
        alias="_id",
        description="The unique identifier for this world.",
        min_length=1,
    )
    owner_id: str = Field(
        ...,
        alias="ownerId",
        description="The unique identifier of the user who created and owns this world.",
        min_length=1,
    )
    name: str = Field(
        ...,
        description="The display name of the world, available as {{world}} macro.",
        min_length=1,
        max_length=200,
    )
    description: str = Field(
        ...,
        description="A brief description of the world that will be shown to users.",
        min_length=1,
        max_length=1000,
    )
    background: str = Field(
        ...,
        description="Detailed world context and background information, available as {{world_background}} macro.",
        min_length=1,
        max_length=5000,
    )

    user_presona_id: str = Field(
        ...,
        alias="userPersonaId",
        description="The unique identifier of the user persona associated with this world.",
        min_length=1,
    )
    characters: List[CharacterInWorld] = Field(
        default_factory=list,
        description="Characters configured to interact within this world.",
    )
    lorebooks: List[LorebookInWorld] = Field(
        default_factory=list,
        description="Lorebooks linked to this world with their specific settings.",
    )

    created_at: datetime = Field(
        ..., alias="createdAt", description="When this world was created."
    )
    updated_at: datetime = Field(
        ..., alias="updatedAt", description="When this world was last modified."
    )

    visibility: WorldVisibilityEnum = Field(
        ...,
        description="Who can see and access this world.",
    )
    forked_from_world_id: Optional[str] = Field(
        None,
        alias="forkedFromWorldId",
        description="The unique identifier of the original world if this is a fork.",
    )
    tags: List[str] = Field(
        default_factory=list,
        description="User-defined tags to categorize and organize this world.",
        max_length=20,
    )

    version: int = Field(
        default=1, description="Schema version for future migration support.", ge=1
    )

    @field_validator("tags")
    @classmethod
    def validate_tags(cls, v: List[str]) -> List[str]:
        if len(v) > 20:
            raise ValueError("Maximum 20 tags allowed")
        validated_tags = []
        for tag in v:
            if not isinstance(tag, str):
                raise ValueError("All tags must be strings")
            tag = tag.strip()
            if len(tag) == 0:
                continue  # Skip empty tags
            if len(tag) > 50:
                raise ValueError("Tag length cannot exceed 50 characters")
            validated_tags.append(tag)
        return validated_tags

    @model_validator(mode="after")
    def validate_timestamps(self) -> Self:
        if self.created_at > self.updated_at:
            raise ValueError("created_at cannot be later than updated_at")
        return self
