from typing import Optional, List, Union, Literal, Dict
from pydantic import BaseModel, Field, HttpUrl, conint, model_validator, field_validator
from datetime import datetime, timezone
from enum import Enum
import uuid


class CharacterVisibilityEnum(str, Enum):
    """Visibility levels for character sharing and access."""

    PRIVATE = "private"
    UNLISTED = "unlisted"
    PUBLIC = "public"


class ActionTrigger(BaseModel):
    """Defines the action to trigger for a tag."""

    action_type: Literal["live2d_motion", "live2d_expression"] = Field(
        description="The type of action to trigger for Live2D models."
    )
    action_index: Optional[Union[str, int]] = Field(
        None, description="The index or identifier of the specific action to trigger."
    )

    class Config:
        frozen = True
        str_strip_whitespace = True


class CharacterImageConfig(BaseModel):
    """Configuration for character's visual representation."""

    live2d_model_url: Optional[HttpUrl] = Field(
        None, description="The URL to the Live2D model file."
    )
    live2d_scale: float = Field(
        default=1.0,
        ge=0.1,
        le=10.0,
        description="The scale factor for the Live2D model display.",
    )
    live2d_initial_x_shift: float = Field(
        default=0.0,
        ge=-1000.0,
        le=1000.0,
        description="The initial horizontal position offset for the Live2D model.",
    )
    live2d_initial_y_shift: float = Field(
        default=0.0,
        ge=-1000.0,
        le=1000.0,
        description="The initial vertical position offset for the Live2D model.",
    )
    live2d_tag_to_action: Dict[str, ActionTrigger] = Field(
        default_factory=dict,
        description="The mapping of text tags to Live2D action triggers.",
    )

    class Config:
        validate_assignment = True
        str_strip_whitespace = True


class CharacterStats(BaseModel):
    """Statistics and engagement metrics for the character."""

    views_count: int = Field(
        default=0,
        ge=0,
        description="The number of times this character has been viewed.",
    )
    like_count: int = Field(
        default=0, ge=0, description="The number of likes this character has received."
    )
    dislike_count: int = Field(
        default=0,
        ge=0,
        description="The number of dislikes this character has received.",
    )
    star_count: int = Field(
        default=0,
        ge=0,
        description="The number of times this character has been starred.",
    )
    fork_count: int = Field(
        default=0,
        ge=0,
        description="The number of times this character has been forked.",
    )

    class Config:
        frozen = True
        validate_assignment = True


class AuthorNote(BaseModel):
    """Author's note configuration for character interactions."""

    content: str = Field(
        min_length=1, max_length=5000, description="The content of the author's note."
    )
    insert_in_conversation: bool = Field(
        default=True,
        description="Whether to insert the author's note during conversations.",
    )
    insertion_frequency: int = Field(
        default=1,
        ge=1,
        le=100,
        description="How often to insert the note based on user message count.",
    )

    @field_validator("content")
    @classmethod
    def validate_content_not_empty(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Author note content cannot be empty or whitespace only")
        return v.strip()

    class Config:
        validate_assignment = True
        str_strip_whitespace = True


class StorybookInWorld(BaseModel):
    """Configuration for how a storybook is used within a character."""

    storybook_id: str = Field(
        description="The unique identifier of the referenced storybook.",
        alias="storybookID",
    )
    enabled: bool = Field(
        default=True, description="Whether this storybook is active for this character."
    )
    max_recursion_depth: int = Field(
        default=0,
        ge=0,
        le=10,
        description="The maximum depth for recursive storybook activation.",
    )

    @field_validator("storybook_id")
    @classmethod
    def validate_storybook_id_not_empty(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Storybook ID cannot be empty")
        return v.strip()

    class Config:
        validate_assignment = True
        str_strip_whitespace = True


class Character(BaseModel):
    """
    A character definition with personality, appearance, and behavior configuration.
    """

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="The unique identifier for this character.",
        alias="_id",
    )
    owner_id: str = Field(
        description="The identifier of the user who owns this character.",
        alias="ownerId",
    )
    forked_from_character_id: Optional[str] = Field(
        None,
        description="The identifier of the original character if this is a fork.",
        alias="forkedFromCharacterId",
    )
    name: str = Field(
        min_length=1,
        max_length=100,
        description="The character's name used in conversations.",
    )
    description: Optional[str] = Field(
        None,
        max_length=2000,
        description="A brief description of the character for users.",
    )
    main_prompt: str = Field(
        min_length=1,
        max_length=50000,
        description="The core personality and behavior definition for the character.",
        alias="mainPrompt",
    )
    personality_prompt: Optional[str] = Field(
        None,
        max_length=10000,
        description="Specific personality traits and characteristics.",
        alias="personalityPrompt",
    )
    example_dialogues: List[str] = Field(
        default_factory=list,
        description="Example conversations showing the character's speaking style.",
        alias="exampleDialogues",
    )
    system_prompt_template: Optional[str] = Field(
        None,
        max_length=20000,
        description="The template for generating system prompts.",
        alias="systemPromptTemplate",
    )
    avatar_url: Optional[HttpUrl] = Field(
        None, description="The URL to the character's avatar image.", alias="avatarUrl"
    )
    image_config: Optional[CharacterImageConfig] = Field(
        None,
        description="Visual configuration including Live2D model settings.",
        alias="imageConfig",
    )
    storybooks: List[StorybookInWorld] = Field(
        default_factory=list,
        description="The storybooks associated with this character.",
    )
    visibility: CharacterVisibilityEnum = Field(
        default=CharacterVisibilityEnum.PRIVATE,
        description="The visibility level for this character.",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this character was created.",
        alias="createdAt",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this character was last modified.",
        alias="updatedAt",
    )
    tags: List[str] = Field(
        default_factory=list,
        description="User-defined tags for categorizing the character.",
    )
    stats: CharacterStats = Field(
        default_factory=CharacterStats,
        description="Engagement statistics for this character.",
    )
    first_message: Optional[str] = Field(
        None,
        max_length=5000,
        description="The initial message the character sends to start conversations.",
        alias="firstMessage",
    )
    author_note: Optional[AuthorNote] = Field(
        None,
        description="Additional notes from the character's author.",
        alias="authorNote",
    )
    version: int = Field(
        default=1, ge=1, description="The version number for migration compatibility."
    )

    @field_validator("name")
    @classmethod
    def validate_name_not_empty(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Character name cannot be empty or whitespace only")
        return v.strip()

    @field_validator("main_prompt")
    @classmethod
    def validate_main_prompt_not_empty(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Main prompt cannot be empty or whitespace only")
        return v.strip()

    @field_validator("owner_id")
    @classmethod
    def validate_owner_id_not_empty(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Owner ID cannot be empty")
        return v.strip()

    @field_validator("tags")
    @classmethod
    def validate_tags(cls, v: List[str]) -> List[str]:
        # Remove empty tags and strip whitespace
        cleaned_tags = [tag.strip() for tag in v if tag.strip()]
        # Remove duplicates while preserving order
        seen = set()
        unique_tags = []
        for tag in cleaned_tags:
            if tag.lower() not in seen:
                seen.add(tag.lower())
                unique_tags.append(tag)
        return unique_tags

    @field_validator("example_dialogues")
    @classmethod
    def validate_example_dialogues(cls, v: List[str]) -> List[str]:
        # Remove empty dialogues and strip whitespace
        return [dialogue.strip() for dialogue in v if dialogue.strip()]

    @model_validator(mode="after")
    def validate_character_consistency(self) -> "Character":
        """Ensure character data consistency."""
        # Update timestamp on any change
        self.updated_at = datetime.now(timezone.utc)

        # Ensure created_at is not after updated_at
        if self.updated_at < self.created_at:
            raise ValueError("Updated timestamp cannot be before creation timestamp")

        # Validate fork relationship
        if self.forked_from_character_id == self.id:
            raise ValueError("Character cannot be forked from itself")

        return self

    class Config:
        use_enum_values = True
        validate_assignment = True
        str_strip_whitespace = True
