from typing import Optional, List, Union, Literal, Self
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from datetime import datetime, timezone
from enum import Enum
import uuid
import re


# Enum for activation strategies
class ActivationStrategyEnum(str, Enum):
    ALWAYS_INSERT = "always_insert"
    FILTER_BASED_INSERT = "filter_based_insert"
    KEYWORD_EXACT_MATCH = "keyword_exact_match"
    KEYWORD_REGEX_MATCH = "keyword_regex_match"
    VECTOR_SIMILARITY_MATCH = "vector_similarity_match"


# --- Filter Condition Primitives and Logical Groups ---


class KeywordExactMatchCondition(BaseModel):
    """Condition for an exact keyword match."""

    model_config = ConfigDict(str_strip_whitespace=True, validate_default=True)

    type: Literal["keyword_exact"] = "keyword_exact"
    keyword: str = Field(
        ...,
        description="The exact keyword to match against.",
        min_length=1,
        max_length=100,
    )
    case_sensitive: bool = Field(
        default=False,
        description="Whether the keyword matching should consider letter case.",
    )

    @field_validator("keyword")
    @classmethod
    def validate_keyword(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Keyword cannot be empty")
        return v.strip()


class KeywordRegexMatchCondition(BaseModel):
    """Condition for a keyword to be matched using a regular expression."""

    model_config = ConfigDict(str_strip_whitespace=True, validate_default=True)

    type: Literal["keyword_regex"] = "keyword_regex"
    regex: str = Field(
        ...,
        description="The regular expression pattern to match against text.",
        min_length=1,
        max_length=500,
    )

    @field_validator("regex")
    @classmethod
    def validate_regex(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Regex pattern cannot be empty")
        try:
            re.compile(v.strip())
        except re.error as e:
            raise ValueError(f"Invalid regex pattern: {e}")
        return v.strip()


class VectorSimilarityCondition(BaseModel):
    """Condition based on vector similarity between a target and the conversation context."""

    model_config = ConfigDict(str_strip_whitespace=True, validate_default=True)

    type: Literal["vector_similarity"] = "vector_similarity"
    text_to_embed: str = Field(
        ...,
        description="Text whose embedding will be compared against the conversation context.",
        min_length=1,
        max_length=1000,
    )
    threshold: float = Field(
        ...,
        ge=0.0,
        le=1.0,
        description="Minimum similarity score required for activation.",
    )

    @field_validator("text_to_embed")
    @classmethod
    def validate_text_to_embed(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Text to embed cannot be empty")
        return v.strip()


# Forward references for recursive types
FilterOperand = Union[
    KeywordExactMatchCondition,
    KeywordRegexMatchCondition,
    VectorSimilarityCondition,
    "AndFilterGroup",
    "OrFilterGroup",
    "NotFilterWrapper",
]


class AndFilterGroup(BaseModel):
    """
    Represents an AND logical group.
    All conditions in the 'all_of' list must be true.
    """

    model_config = ConfigDict(validate_default=True)

    all_of: List[FilterOperand] = Field(
        ...,
        min_length=1,
        max_length=10,  # Reasonable limit to prevent overly complex conditions
        description="All of these conditions must be satisfied for activation.",
    )


class OrFilterGroup(BaseModel):
    """
    Represents an OR logical group.
    At least one condition in the 'any_of' list must be true.
    """

    model_config = ConfigDict(validate_default=True)

    any_of: List[FilterOperand] = Field(
        ...,
        min_length=1,
        max_length=10,  # Reasonable limit to prevent overly complex conditions
        description="At least one of these conditions must be satisfied for activation.",
    )


class NotFilterWrapper(BaseModel):
    """
    Represents a NOT logical operation.
    The 'not_this' condition must evaluate to false.
    """

    model_config = ConfigDict(validate_default=True)

    not_this: FilterOperand = Field(
        ..., description="This condition must not be satisfied for activation."
    )


# Rebuild models to handle forward references
AndFilterGroup.model_rebuild()
OrFilterGroup.model_rebuild()
NotFilterWrapper.model_rebuild()


class LoreEntry(BaseModel):
    """Lore entry in a Lorebook"""

    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
        str_strip_whitespace=True,
        validate_default=True,
        frozen=False,
    )

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="The unique identifier for this lore entry.",
    )
    enabled: bool = Field(
        default=True,
        description="Whether this lore entry is active and can be triggered.",
    )

    activation_strategy: ActivationStrategyEnum = Field(
        ..., description="How this lore entry should be triggered for inclusion."
    )

    filter_conditions: Optional[FilterOperand] = Field(
        default=None,
        description="Logical conditions that determine when this entry activates.",
    )

    entry_content: str = Field(
        ...,
        description="The text content that will be injected into the AI's context when this entry activates.",
        min_length=1,
        max_length=10000,
    )

    insertion_order: int = Field(
        default=0,
        description="Priority for insertion order, where lower numbers are inserted first.",
    )
    insertion_probability: float = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="Probability that this entry will be inserted when conditions are met.",
    )
    duration_turns: Optional[int] = Field(
        default=None,
        ge=0,
        le=100,  # Reasonable upper limit
        description="Number of conversation turns this entry remains active after insertion.",
    )
    cooldown_turns: Optional[int] = Field(
        default=None,
        ge=0,
        le=100,  # Reasonable upper limit
        description="Number of turns to wait before this entry can be activated again.",
    )
    scan_depth: int = Field(
        default=10,
        ge=1,
        le=50,  # Reasonable upper limit for performance
        description="Number of recent messages to scan when checking activation conditions.",
    )

    notes: Optional[str] = Field(
        default=None,
        max_length=1000,
        description="Personal notes about this lore entry for reference.",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this lore entry was created.",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this lore entry was last modified.",
    )

    @field_validator("entry_content")
    @classmethod
    def validate_entry_content(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Entry content cannot be empty")
        return v.strip()

    @model_validator(mode="after")
    def validate_logic(self) -> Self:
        """Validate that filter_conditions is provided when activation_strategy is 'filter_based_insert'."""
        if (
            self.activation_strategy == ActivationStrategyEnum.FILTER_BASED_INSERT
            and self.filter_conditions is None
        ):
            raise ValueError(
                "filter_conditions must be provided when activation_strategy is 'filter_based_insert'."
            )

        if self.created_at > self.updated_at:
            raise ValueError("created_at cannot be later than updated_at")

        return self


class Lorebook(BaseModel):
    """
    Represents a collection of lore entries and their shared settings for a specific world.
    """

    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
        str_strip_whitespace=True,
        validate_default=True,
        frozen=False,
    )

    id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="The unique identifier for this lorebook.",
    )

    name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="The display name for this lorebook.",
    )
    description: Optional[str] = Field(
        default=None,
        max_length=500,
        description="A brief description of what this lorebook contains and its purpose.",
    )

    entries: List[LoreEntry] = Field(
        default_factory=list,
        description="The lore entries contained in this lorebook.",
        max_length=1000,  # Reasonable limit for performance
    )

    # Metadata for the lorebook
    owner_id: Optional[str] = Field(
        default=None,
        description="The unique identifier of the user who owns this lorebook.",
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this lorebook was created.",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When this lorebook was last modified.",
    )

    version: int = Field(
        default=1, description="Schema version for future migration support.", ge=1
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Lorebook name cannot be empty")
        return v.strip()

    @model_validator(mode="after")
    def validate_timestamps(self) -> Self:
        if self.created_at > self.updated_at:
            raise ValueError("created_at cannot be later than updated_at")
        return self


class LorebookInWorld(BaseModel):
    """
    Defines how a specific Lorebook is used within a World.
    """

    model_config = ConfigDict(
        populate_by_name=True,
        str_strip_whitespace=True,
        validate_default=True,
        frozen=False,
    )

    lorebook_id: str = Field(
        ...,
        alias="lorebookID",
        min_length=1,
    )
    enabled: bool = Field(
        True,
    )
    max_recursion_depth: int = Field(
        default=0,
        ge=0,
        le=10,  # Reasonable upper limit to prevent infinite recursion
        description="Maximum depth for recursive activation. Zero means only direct triggers, higher values allow cascading activations.",
    )

    @field_validator("lorebook_id")
    @classmethod
    def validate_lorebook_id(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Lorebook ID cannot be empty")
        return v.strip()
