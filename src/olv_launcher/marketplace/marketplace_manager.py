"""
Marketplace manager for plugin installation and updates.

This module manages the plugin marketplace functionality including
plugin discovery, installation, updates, and version management.
"""

import logging
import shutil
from typing import List, Dict, Optional
from pathlib import Path
from datetime import datetime

from .github_client import GitHubAPIClient
from ..models.marketplace import (
    MarketplacePluginInfo,
    MarketplacePluginStatus,
    PluginInstallationProgress,
    InstallationStatus,
    PluginUpdateInfo,
    MarketplaceSearchFilter
)
from ..models.config import settings

from ..exceptions import ServiceError, PluginNotFoundError
from ..plugin_manager import UnifiedPluginManager

logger = logging.getLogger(__name__)


class MarketplaceManager:
    """Manager for plugin marketplace operations"""

    def __init__(self, plugin_manager: UnifiedPluginManager):
        """
        Initialize marketplace manager.

        Args:
            plugin_manager: The unified plugin manager instance
        """
        self.plugin_manager = plugin_manager
        self.github_client = GitHubAPIClient(
            settings.plugin_marketplace_url,
            settings.plugin_marketplace_branch
        )

        # Track installation progress
        self.installation_progress: Dict[str, PluginInstallationProgress] = {}

        # Cache for marketplace plugins
        self._marketplace_plugins: List[MarketplacePluginInfo] = []
        self._last_refresh: Optional[datetime] = None

    async def refresh_marketplace(self, force: bool = False) -> List[MarketplacePluginInfo]:
        """
        Refresh marketplace plugin list.

        Args:
            force: Whether to force refresh ignoring cache

        Returns:
            List of marketplace plugins
        """
        logger.info("Refreshing marketplace plugin list")

        try:
            # Get plugins from GitHub
            marketplace_plugins = await self.github_client.get_plugins(force_refresh=force)

            # Update status based on local installations
            await self._update_plugin_statuses(marketplace_plugins)

            self._marketplace_plugins = marketplace_plugins
            self._last_refresh = datetime.now()

            logger.info(f"Refreshed marketplace with {len(marketplace_plugins)} plugins")
            return marketplace_plugins

        except Exception as e:
            logger.error(f"Failed to refresh marketplace: {e}")
            raise ServiceError(f"Failed to refresh marketplace: {e}")

    async def get_marketplace_plugins(self, filter_options: Optional[MarketplaceSearchFilter] = None) -> List[MarketplacePluginInfo]:
        """
        Get marketplace plugins with optional filtering.

        Args:
            filter_options: Search and filter options

        Returns:
            Filtered list of marketplace plugins
        """
        # Refresh if needed
        if not self._marketplace_plugins or not self._last_refresh:
            await self.refresh_marketplace()

        plugins = self._marketplace_plugins.copy()

        # Apply filters if provided
        if filter_options:
            plugins = self._apply_filters(plugins, filter_options)

        return plugins

    def _apply_filters(self, plugins: List[MarketplacePluginInfo], filters: MarketplaceSearchFilter) -> List[MarketplacePluginInfo]:
        """Apply search and filter options to plugin list"""
        filtered_plugins = plugins

        # Text search
        if filters.query:
            query_lower = filters.query.lower()
            filtered_plugins = [
                p for p in filtered_plugins
                if query_lower in p.name.lower()
                or query_lower in p.description.lower()
                or query_lower in p.author.lower()
            ]

        # Service type filter
        if filters.service_type:
            filtered_plugins = [
                p for p in filtered_plugins
                if p.service_type == filters.service_type
            ]

        # Tags filter
        if filters.tags:
            filtered_plugins = [
                p for p in filtered_plugins
                if any(tag in p.tags for tag in filters.tags)
            ]

        # Author filter
        if filters.author:
            filtered_plugins = [
                p for p in filtered_plugins
                if filters.author.lower() in p.author.lower()
            ]

        # Pagination
        start_idx = filters.offset
        end_idx = start_idx + filters.limit
        filtered_plugins = filtered_plugins[start_idx:end_idx]

        return filtered_plugins

    async def _update_plugin_statuses(self, marketplace_plugins: List[MarketplacePluginInfo]):
        """Update plugin statuses based on local installations"""
        local_plugins = self.plugin_manager.plugins

        for plugin in marketplace_plugins:
            if plugin.name in local_plugins:
                local_plugin = local_plugins[plugin.name]
                plugin.installed_version = local_plugin.version

                # Compare versions to determine status
                if self._is_version_newer(plugin.version, local_plugin.version):
                    plugin.status = MarketplacePluginStatus.UPDATE_AVAILABLE
                else:
                    plugin.status = MarketplacePluginStatus.INSTALLED
            else:
                plugin.status = MarketplacePluginStatus.AVAILABLE

    def _is_version_newer(self, new_version: str, current_version: str) -> bool:
        """Compare version strings to determine if new version is newer"""
        try:
            # Simple version comparison (assumes semantic versioning)
            new_parts = [int(x) for x in new_version.split('.')]
            current_parts = [int(x) for x in current_version.split('.')]

            # Pad shorter version with zeros
            max_len = max(len(new_parts), len(current_parts))
            new_parts.extend([0] * (max_len - len(new_parts)))
            current_parts.extend([0] * (max_len - len(current_parts)))

            return new_parts > current_parts
        except (ValueError, AttributeError):
            # Fallback to string comparison if version parsing fails
            return new_version > current_version

    async def install_plugin(self, plugin_name: str) -> PluginInstallationProgress:
        """
        Install a plugin from the marketplace.

        Args:
            plugin_name: Name of the plugin to install

        Returns:
            Installation progress information
        """
        # Find plugin in marketplace
        marketplace_plugin = None
        for plugin in self._marketplace_plugins:
            if plugin.name == plugin_name:
                marketplace_plugin = plugin
                break

        if not marketplace_plugin:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found in marketplace")

        if marketplace_plugin.status == MarketplacePluginStatus.INSTALLED:
            raise ServiceError(f"Plugin '{plugin_name}' is already installed")

        # Initialize progress tracking
        progress = PluginInstallationProgress(
            plugin_name=plugin_name,
            status=InstallationStatus.PENDING,
            total_steps=4,  # download, extract, install deps, configure
            current_step="Initializing installation"
        )
        self.installation_progress[plugin_name] = progress

        try:
            # Update status to downloading
            progress.status = InstallationStatus.DOWNLOADING
            progress.current_step = "Downloading plugin files"
            progress.current_step_index = 1
            progress.progress_percentage = 25.0

            # Download plugin
            target_dir = Path(settings.plugins_dir) / marketplace_plugin.service_type
            plugin_dir = await self.github_client.download_plugin(marketplace_plugin, target_dir)

            # Update status to extracting
            progress.status = InstallationStatus.EXTRACTING
            progress.current_step = "Extracting plugin files"
            progress.current_step_index = 2
            progress.progress_percentage = 50.0

            # Install dependencies if needed
            progress.status = InstallationStatus.INSTALLING_DEPS
            progress.current_step = "Installing dependencies"
            progress.current_step_index = 3
            progress.progress_percentage = 75.0

            await self._install_plugin_dependencies(plugin_dir)

            # Configure plugin
            progress.status = InstallationStatus.CONFIGURING
            progress.current_step = "Configuring plugin"
            progress.current_step_index = 4
            progress.progress_percentage = 90.0

            # Rediscover plugins to include the new one
            self.plugin_manager.discover_plugins()

            # Complete installation
            progress.status = InstallationStatus.COMPLETED
            progress.current_step = "Installation completed"
            progress.progress_percentage = 100.0
            progress.completed_at = datetime.now()

            # Force refresh plugin status after installation
            await self._update_plugin_statuses(self._marketplace_plugins)

            logger.info(f"Successfully installed plugin {plugin_name}")
            return progress

        except Exception as e:
            logger.error(f"Failed to install plugin {plugin_name}: {e}")
            progress.status = InstallationStatus.FAILED
            progress.error_message = str(e)
            progress.completed_at = datetime.now()
            raise ServiceError(f"Failed to install plugin: {e}")

    async def _install_plugin_dependencies(self, plugin_dir: Path):
        """Install plugin dependencies using the appropriate package manager"""
        pyproject_file = plugin_dir / "pyproject.toml"
        requirements_file = plugin_dir / "requirements.txt"

        if pyproject_file.exists():
            # Use UV for pyproject.toml
            if settings.enable_uv_sync:
                try:
                    import subprocess
                    result = subprocess.run(
                        ["uv", "sync"],
                        cwd=plugin_dir,
                        capture_output=True,
                        text=True,
                        timeout=settings.uv_sync_timeout
                    )
                    if result.returncode != 0:
                        logger.warning(f"UV sync failed: {result.stderr}")
                except Exception as e:
                    logger.warning(f"Failed to run UV sync: {e}")

        elif requirements_file.exists():
            # Use pip for requirements.txt
            try:
                import subprocess
                result = subprocess.run(
                    ["pip", "install", "-r", str(requirements_file)],
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                if result.returncode != 0:
                    logger.warning(f"Pip install failed: {result.stderr}")
            except Exception as e:
                logger.warning(f"Failed to install requirements: {e}")

    async def uninstall_plugin(self, plugin_name: str) -> bool:
        """
        Uninstall a plugin.

        Args:
            plugin_name: Name of the plugin to uninstall

        Returns:
            True if successful
        """
        if plugin_name not in self.plugin_manager.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' is not installed")

        plugin_info = self.plugin_manager.plugins[plugin_name]

        try:
            # Stop plugin if running
            if self.plugin_manager.is_plugin_running(plugin_name):
                await self.plugin_manager.stop_plugin_service(plugin_name)

            # Remove plugin directory
            if plugin_info.plugin_dir and plugin_info.plugin_dir.exists():
                shutil.rmtree(plugin_info.plugin_dir)

            # Rediscover plugins to update the list
            self.plugin_manager.discover_plugins()

            # Force refresh plugin status after uninstallation
            await self._update_plugin_statuses(self._marketplace_plugins)

            logger.info(f"Successfully uninstalled plugin {plugin_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to uninstall plugin {plugin_name}: {e}")
            raise ServiceError(f"Failed to uninstall plugin: {e}")

    async def check_updates(self, plugin_names: Optional[List[str]] = None) -> List[PluginUpdateInfo]:
        """
        Check for plugin updates.

        Args:
            plugin_names: Specific plugins to check, or None for all

        Returns:
            List of update information
        """
        await self.refresh_marketplace(force=True)

        updates = []
        local_plugins = self.plugin_manager.plugins

        plugins_to_check = plugin_names or list(local_plugins.keys())

        for plugin_name in plugins_to_check:
            if plugin_name not in local_plugins:
                continue

            local_plugin = local_plugins[plugin_name]

            # Find corresponding marketplace plugin
            marketplace_plugin = None
            for mp in self._marketplace_plugins:
                if mp.name == plugin_name:
                    marketplace_plugin = mp
                    break

            if marketplace_plugin:
                update_available = self._is_version_newer(
                    marketplace_plugin.version,
                    local_plugin.version
                )

                update_info = PluginUpdateInfo(
                    plugin_name=plugin_name,
                    current_version=local_plugin.version,
                    latest_version=marketplace_plugin.version,
                    update_available=update_available
                )
                updates.append(update_info)

        return updates

    async def update_plugin(self, plugin_name: str) -> PluginInstallationProgress:
        """
        Update a plugin to the latest version.

        Args:
            plugin_name: Name of the plugin to update

        Returns:
            Update progress information
        """
        if plugin_name not in self.plugin_manager.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' is not installed")

        # Find marketplace plugin
        marketplace_plugin = None
        for plugin in self._marketplace_plugins:
            if plugin.name == plugin_name:
                marketplace_plugin = plugin
                break

        if not marketplace_plugin:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found in marketplace")

        # Check if update is needed
        local_plugin = self.plugin_manager.plugins[plugin_name]
        if not self._is_version_newer(marketplace_plugin.version, local_plugin.version):
            raise ServiceError(f"Plugin '{plugin_name}' is already up to date")

        # Stop plugin if running
        was_running = self.plugin_manager.is_plugin_running(plugin_name)
        if was_running:
            await self.plugin_manager.stop_plugin_service(plugin_name)

        try:
            # Backup current plugin
            backup_dir = local_plugin.plugin_dir.parent / f"{plugin_name}_backup"
            if backup_dir.exists():
                shutil.rmtree(backup_dir)
            shutil.copytree(local_plugin.plugin_dir, backup_dir)

            # Remove current plugin
            shutil.rmtree(local_plugin.plugin_dir)

            # Install new version
            progress = await self.install_plugin(plugin_name)

            # Force refresh plugin status after update
            await self._update_plugin_statuses(self._marketplace_plugins)

            # Restart plugin if it was running
            if was_running:
                await self.plugin_manager.start_plugin_service(plugin_name)

            # Clean up backup
            shutil.rmtree(backup_dir)

            logger.info(f"Successfully updated plugin {plugin_name} to version {marketplace_plugin.version}")
            return progress

        except Exception as e:
            # Restore backup on failure
            if 'backup_dir' in locals() and backup_dir.exists():
                if local_plugin.plugin_dir.exists():
                    shutil.rmtree(local_plugin.plugin_dir)
                shutil.copytree(backup_dir, local_plugin.plugin_dir)
                shutil.rmtree(backup_dir)

            logger.error(f"Failed to update plugin {plugin_name}: {e}")
            raise ServiceError(f"Failed to update plugin: {e}")

    def get_installation_progress(self, plugin_name: str) -> Optional[PluginInstallationProgress]:
        """Get installation progress for a plugin"""
        return self.installation_progress.get(plugin_name)

    def clear_installation_progress(self, plugin_name: str):
        """Clear installation progress for a plugin"""
        if plugin_name in self.installation_progress:
            del self.installation_progress[plugin_name]

    async def force_refresh_plugin_statuses(self):
        """Force refresh all plugin statuses based on current local installations"""
        if self._marketplace_plugins:
            await self._update_plugin_statuses(self._marketplace_plugins)
            logger.info("Plugin statuses refreshed")
