"""
GitHub API client for plugin marketplace.

This module provides functionality to interact with GitHub repositories
to fetch plugin information and download plugin files.
"""

import asyncio
import json
import logging
from typing import List, Dict, Optional, Any
from pathlib import Path
from urllib.parse import urlparse
import httpx
from datetime import datetime, timedelta

from ..models.marketplace import MarketplacePluginInfo, MarketplacePluginStatus, MarketplaceCache
from ..models.config import settings
from ..exceptions import ServiceError

logger = logging.getLogger(__name__)


class GitHubAPIClient:
    """Client for interacting with GitHub API to fetch plugin marketplace data"""
    
    def __init__(self, marketplace_url: str, branch: str = "main"):
        """
        Initialize GitHub API client.
        
        Args:
            marketplace_url: GitHub repository URL
            branch: Repository branch to use
        """
        self.marketplace_url = marketplace_url
        self.branch = branch
        self.api_base_url = "https://api.github.com"
        
        # Parse repository info from URL
        parsed_url = urlparse(marketplace_url)
        path_parts = parsed_url.path.strip('/').split('/')
        if len(path_parts) >= 2:
            self.owner = path_parts[0]
            self.repo = path_parts[1]
        else:
            raise ValueError(f"Invalid GitHub repository URL: {marketplace_url}")
        
        # Cache for repository data
        self._cache: Optional[MarketplaceCache] = None
        
    async def get_plugins(self, force_refresh: bool = False) -> List[MarketplacePluginInfo]:
        """
        Get all plugins from the marketplace repository.
        
        Args:
            force_refresh: Whether to force refresh the cache
            
        Returns:
            List of marketplace plugin information
        """
        # Check cache first
        if not force_refresh and self._is_cache_valid():
            logger.info("Using cached marketplace data")
            return self._cache.plugins
        
        logger.info(f"Fetching plugins from {self.marketplace_url}")
        
        try:
            plugins = []
            
            # Get service type directories (asr, tts, llm)
            service_types = ["asr", "tts", "llm"]
            
            for service_type in service_types:
                service_plugins = await self._get_service_plugins(service_type)
                plugins.extend(service_plugins)
            
            # Update cache
            self._update_cache(plugins)
            
            logger.info(f"Found {len(plugins)} plugins in marketplace")
            return plugins
            
        except Exception as e:
            logger.error(f"Failed to fetch plugins from marketplace: {e}")
            # Return cached data if available
            if self._cache and self._cache.plugins:
                logger.warning("Returning cached data due to fetch error")
                return self._cache.plugins
            raise ServiceError(f"Failed to fetch marketplace plugins: {e}")
    
    async def _get_service_plugins(self, service_type: str) -> List[MarketplacePluginInfo]:
        """Get plugins for a specific service type"""
        plugins = []
        
        try:
            # Get directory contents for service type
            url = f"{self.api_base_url}/repos/{self.owner}/{self.repo}/contents/{service_type}"
            params = {"ref": self.branch}
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, params=params)
                
                if response.status_code == 404:
                    logger.warning(f"Service type directory not found: {service_type}")
                    return plugins
                
                response.raise_for_status()
                contents = response.json()
            
            # Process each plugin directory
            for item in contents:
                if item["type"] == "dir":
                    plugin_name = item["name"]
                    try:
                        plugin_info = await self._get_plugin_info(service_type, plugin_name)
                        if plugin_info:
                            plugins.append(plugin_info)
                    except Exception as e:
                        logger.error(f"Failed to get info for plugin {plugin_name}: {e}")
                        continue
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching {service_type} plugins: {e}")
        except Exception as e:
            logger.error(f"Error fetching {service_type} plugins: {e}")
        
        return plugins
    
    async def _get_plugin_info(self, service_type: str, plugin_name: str) -> Optional[MarketplacePluginInfo]:
        """Get information for a specific plugin"""
        try:
            # Get plugin.json file
            config_url = f"{self.api_base_url}/repos/{self.owner}/{self.repo}/contents/{service_type}/{plugin_name}/plugin.json"
            params = {"ref": self.branch}
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(config_url, params=params)
                
                if response.status_code == 404:
                    logger.warning(f"plugin.json not found for {plugin_name}")
                    return None
                
                response.raise_for_status()
                file_info = response.json()
            
            # Decode base64 content
            import base64
            config_content = base64.b64decode(file_info["content"]).decode("utf-8")
            config_data = json.loads(config_content)
            
            # Create download URL for the plugin directory
            download_url = f"{self.marketplace_url}/archive/refs/heads/{self.branch}.zip"
            
            # Create plugin info
            plugin_info = MarketplacePluginInfo(
                name=plugin_name,
                service_type=service_type,
                version=config_data.get("version", "1.0.0"),
                description=config_data.get("description", ""),
                author=config_data.get("author", ""),
                repository_url=self.marketplace_url,
                download_url=download_url,
                file_size=file_info.get("size"),
                dependencies=config_data.get("dependencies", []),
                tags=config_data.get("tags", []),
                license=config_data.get("license", ""),
                marketplace_url=self.marketplace_url,
                plugin_path=f"{service_type}/{plugin_name}",
                status=MarketplacePluginStatus.AVAILABLE
            )
            
            return plugin_info
            
        except Exception as e:
            logger.error(f"Failed to get plugin info for {plugin_name}: {e}")
            return None
    
    async def download_plugin(self, plugin_info: MarketplacePluginInfo, target_dir: Path) -> Path:
        """
        Download a plugin from the marketplace.
        
        Args:
            plugin_info: Plugin information
            target_dir: Target directory for download
            
        Returns:
            Path to downloaded plugin directory
        """
        logger.info(f"Downloading plugin {plugin_info.name} from {plugin_info.download_url}")
        
        try:
            # Create target directory
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Download the repository archive
            async with httpx.AsyncClient(timeout=settings.plugin_download_timeout) as client:
                response = await client.get(plugin_info.download_url)
                response.raise_for_status()
                
                # Save to temporary zip file
                zip_path = target_dir / f"{plugin_info.name}.zip"
                with open(zip_path, "wb") as f:
                    f.write(response.content)
            
            # Extract the specific plugin directory
            import zipfile
            import shutil
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Find the plugin directory in the archive
                archive_plugin_path = None
                for name in zip_ref.namelist():
                    if name.endswith(f"{plugin_info.plugin_path}/"):
                        archive_plugin_path = name
                        break
                
                if not archive_plugin_path:
                    raise ServiceError(f"Plugin directory not found in archive: {plugin_info.plugin_path}")
                
                # Extract plugin files
                plugin_target_dir = target_dir / plugin_info.name
                plugin_target_dir.mkdir(exist_ok=True)
                
                for member in zip_ref.namelist():
                    if member.startswith(archive_plugin_path) and member != archive_plugin_path:
                        # Calculate relative path within plugin directory
                        relative_path = member[len(archive_plugin_path):]
                        if relative_path:
                            target_file = plugin_target_dir / relative_path
                            target_file.parent.mkdir(parents=True, exist_ok=True)
                            
                            if not member.endswith('/'):
                                with zip_ref.open(member) as source, open(target_file, 'wb') as target:
                                    shutil.copyfileobj(source, target)
            
            # Clean up zip file
            zip_path.unlink()
            
            logger.info(f"Successfully downloaded plugin {plugin_info.name} to {plugin_target_dir}")
            return plugin_target_dir
            
        except Exception as e:
            logger.error(f"Failed to download plugin {plugin_info.name}: {e}")
            raise ServiceError(f"Failed to download plugin: {e}")
    
    def _is_cache_valid(self) -> bool:
        """Check if the current cache is still valid"""
        if not self._cache:
            return False
        
        return datetime.now() < self._cache.expires_at
    
    def _update_cache(self, plugins: List[MarketplacePluginInfo]):
        """Update the plugin cache"""
        expires_at = datetime.now() + timedelta(seconds=settings.plugin_marketplace_cache_ttl)
        
        self._cache = MarketplaceCache(
            marketplace_url=self.marketplace_url,
            expires_at=expires_at,
            plugins=plugins
        )
    
    async def get_plugin_details(self, service_type: str, plugin_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific plugin"""
        try:
            # Get README file if available
            readme_url = f"{self.api_base_url}/repos/{self.owner}/{self.repo}/contents/{service_type}/{plugin_name}/README.md"
            params = {"ref": self.branch}
            
            readme_content = ""
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(readme_url, params=params)
                if response.status_code == 200:
                    file_info = response.json()
                    import base64
                    readme_content = base64.b64decode(file_info["content"]).decode("utf-8")
            
            return {
                "readme": readme_content,
                "repository_url": f"{self.marketplace_url}/tree/{self.branch}/{service_type}/{plugin_name}"
            }
            
        except Exception as e:
            logger.error(f"Failed to get plugin details for {plugin_name}: {e}")
            return None
