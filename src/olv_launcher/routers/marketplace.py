"""
Marketplace API routes for OLV Launcher.

This module provides REST API endpoints for plugin marketplace functionality
including plugin discovery, installation, updates, and management.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query, Body
from datetime import datetime

from ..models.api import (
    ErrorResponse,
    MarketplacePluginApiInfo,
    MarketplaceSearchRequest,
    MarketplacePluginListResponse,
    PluginInstallRequest,
    PluginInstallResponse,
    PluginInstallProgressResponse,
    PluginUninstallRequest,
    PluginUninstallResponse,
    PluginUpdateCheckResponse,
    PluginUpdateRequest,
    PluginUpdateResponse,
    MarketplaceRefreshResponse,
)
from ..models.marketplace import MarketplaceSearchFilter, InstallationStatus
from ..marketplace.marketplace_manager import MarketplaceManager
from ..exceptions import PluginNotFoundError, ServiceError

logger = logging.getLogger(__name__)

# Global marketplace manager instance (will be set by main app)
marketplace_manager: Optional[MarketplaceManager] = None

router = APIRouter(prefix="/marketplace", tags=["marketplace"])


def set_marketplace_manager(manager: MarketplaceManager):
    """Set the global marketplace manager instance"""
    global marketplace_manager
    marketplace_manager = manager


@router.get(
    "/plugins",
    response_model=MarketplacePluginListResponse,
    summary="Get marketplace plugins",
    description="Get list of plugins from the marketplace with optional filtering and search"
)
async def get_marketplace_plugins(
    query: Optional[str] = Query(None, description="Search query"),
    service_type: Optional[str] = Query(None, description="Filter by service type"),
    author: Optional[str] = Query(None, description="Filter by author"),
    min_rating: Optional[float] = Query(None, description="Minimum rating filter"),
    sort_by: str = Query("name", description="Sort field (name, rating, downloads, updated)"),
    sort_order: str = Query("asc", description="Sort order (asc, desc)"),
    limit: int = Query(50, description="Maximum number of results", ge=1, le=100),
    offset: int = Query(0, description="Result offset for pagination", ge=0),
    tags: List[str] = Query([], description="Filter by tags")
):
    """Get marketplace plugins with optional filtering"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    try:
        # Create filter options
        filter_options = MarketplaceSearchFilter(
            query=query,
            service_type=service_type,
            tags=tags,
            author=author,
            min_rating=min_rating,
            sort_by=sort_by,
            sort_order=sort_order,
            limit=limit,
            offset=offset
        )

        # Get filtered plugins
        plugins = await marketplace_manager.get_marketplace_plugins(filter_options)

        # Convert to API format
        api_plugins = []
        for plugin in plugins:
            api_plugin = MarketplacePluginApiInfo(
                name=plugin.name,
                service_type=plugin.service_type,
                version=plugin.version,
                description=plugin.description,
                author=plugin.author,
                repository_url=plugin.repository_url,
                download_url=plugin.download_url,
                file_size=plugin.file_size,
                dependencies=plugin.dependencies,
                tags=plugin.tags,
                license=plugin.license,
                created_at=plugin.created_at,
                updated_at=plugin.updated_at,
                download_count=plugin.download_count,
                rating=plugin.rating,
                status=plugin.status.value,
                installed_version=plugin.installed_version
            )
            api_plugins.append(api_plugin)

        # Calculate total count and has_more
        total_count = len(api_plugins)
        has_more = total_count == limit  # Simple heuristic

        return MarketplacePluginListResponse(
            plugins=api_plugins,
            total_count=total_count,
            has_more=has_more
        )

    except Exception as e:
        logger.error(f"Failed to get marketplace plugins: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get marketplace plugins: {str(e)}")


@router.post(
    "/refresh",
    response_model=MarketplaceRefreshResponse,
    summary="Refresh marketplace",
    description="Refresh the marketplace plugin list from the remote repository"
)
async def refresh_marketplace(force: bool = Query(False, description="Force refresh ignoring cache")):
    """Refresh marketplace plugin list"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    try:
        plugins = await marketplace_manager.refresh_marketplace(force=force)

        return MarketplaceRefreshResponse(
            message="Marketplace refreshed successfully",
            plugin_count=len(plugins),
            last_updated=datetime.now()
        )

    except Exception as e:
        logger.error(f"Failed to refresh marketplace: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to refresh marketplace: {str(e)}")


@router.post(
    "/install",
    response_model=PluginInstallResponse,
    summary="Install plugin",
    description="Install a plugin from the marketplace"
)
async def install_plugin(
    background_tasks: BackgroundTasks,
    request: PluginInstallRequest = Body(...)
):
    """Install a plugin from the marketplace"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    try:
        # Start installation in background
        background_tasks.add_task(
            _install_plugin_background,
            request.plugin_name
        )

        return PluginInstallResponse(
            message=f"Plugin installation started for {request.plugin_name}",
            plugin_name=request.plugin_name,
            status="installing",
            progress_id=request.plugin_name  # Use plugin name as progress ID
        )

    except PluginNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to start plugin installation: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start installation: {str(e)}")


async def _install_plugin_background(plugin_name: str):
    """Background task for plugin installation"""
    try:
        await marketplace_manager.install_plugin(plugin_name)
        logger.info(f"Plugin {plugin_name} installed successfully")
    except Exception as e:
        logger.error(f"Background installation failed for {plugin_name}: {e}")


@router.get(
    "/install/{plugin_name}/progress",
    response_model=PluginInstallProgressResponse,
    summary="Get installation progress",
    description="Get the installation progress for a plugin"
)
async def get_installation_progress(plugin_name: str):
    """Get installation progress for a plugin"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    progress = marketplace_manager.get_installation_progress(plugin_name)

    if not progress:
        raise HTTPException(status_code=404, detail=f"No installation progress found for {plugin_name}")

    return PluginInstallProgressResponse(
        plugin_name=progress.plugin_name,
        status=progress.status.value,
        progress_percentage=progress.progress_percentage,
        current_step=progress.current_step,
        total_steps=progress.total_steps,
        current_step_index=progress.current_step_index,
        error_message=progress.error_message,
        started_at=progress.started_at,
        completed_at=progress.completed_at
    )


@router.post(
    "/uninstall",
    response_model=PluginUninstallResponse,
    summary="Uninstall plugin",
    description="Uninstall a plugin"
)
async def uninstall_plugin(request: PluginUninstallRequest = Body(...)):
    """Uninstall a plugin"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    try:
        success = await marketplace_manager.uninstall_plugin(request.plugin_name)

        if success:
            return PluginUninstallResponse(
                message=f"Plugin {request.plugin_name} uninstalled successfully",
                plugin_name=request.plugin_name
            )
        else:
            raise HTTPException(status_code=500, detail="Uninstallation failed")

    except PluginNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to uninstall plugin: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to uninstall plugin: {str(e)}")


@router.get(
    "/updates",
    response_model=List[PluginUpdateCheckResponse],
    summary="Check for updates",
    description="Check for updates for all installed plugins or specific plugins"
)
async def check_updates(
    plugin_names: Optional[List[str]] = Query(None, description="Specific plugins to check")
):
    """Check for plugin updates"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    try:
        updates = await marketplace_manager.check_updates(plugin_names)

        response = []
        for update in updates:
            response.append(PluginUpdateCheckResponse(
                plugin_name=update.plugin_name,
                current_version=update.current_version,
                latest_version=update.latest_version,
                update_available=update.update_available,
                changelog=update.changelog
            ))

        return response

    except Exception as e:
        logger.error(f"Failed to check updates: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to check updates: {str(e)}")


@router.post(
    "/update",
    response_model=PluginUpdateResponse,
    summary="Update plugin",
    description="Update a plugin to the latest version"
)
async def update_plugin(
    background_tasks: BackgroundTasks,
    request: PluginUpdateRequest = Body(...)
):
    """Update a plugin to the latest version"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    try:
        # Get current version
        local_plugins = marketplace_manager.plugin_manager.plugins
        if request.plugin_name not in local_plugins:
            raise HTTPException(status_code=404, detail=f"Plugin {request.plugin_name} is not installed")

        current_version = local_plugins[request.plugin_name].version

        # Start update in background
        background_tasks.add_task(
            _update_plugin_background,
            request.plugin_name
        )

        return PluginUpdateResponse(
            message=f"Plugin update started for {request.plugin_name}",
            plugin_name=request.plugin_name,
            old_version=current_version,
            new_version="updating...",  # Will be updated after completion
            progress_id=request.plugin_name
        )

    except PluginNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to start plugin update: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start update: {str(e)}")


async def _update_plugin_background(plugin_name: str):
    """Background task for plugin update"""
    try:
        await marketplace_manager.update_plugin(plugin_name)
        logger.info(f"Plugin {plugin_name} updated successfully")
    except Exception as e:
        logger.error(f"Background update failed for {plugin_name}: {e}")


@router.delete(
    "/install/{plugin_name}/progress",
    summary="Clear installation progress",
    description="Clear the installation progress for a plugin"
)
async def clear_installation_progress(plugin_name: str):
    """Clear installation progress for a plugin"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    marketplace_manager.clear_installation_progress(plugin_name)

    return {"message": f"Installation progress cleared for {plugin_name}"}


@router.post(
    "/refresh-statuses",
    summary="Refresh plugin statuses",
    description="Force refresh all plugin statuses based on current local installations"
)
async def refresh_plugin_statuses():
    """Force refresh all plugin statuses"""
    if not marketplace_manager:
        raise HTTPException(status_code=500, detail="Marketplace manager not initialized")

    try:
        await marketplace_manager.force_refresh_plugin_statuses()
        return {"message": "Plugin statuses refreshed successfully"}
    except Exception as e:
        logger.error(f"Failed to refresh plugin statuses: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to refresh plugin statuses: {str(e)}")
