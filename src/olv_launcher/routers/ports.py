"""
Port management routes for OLV Launcher.

This module contains all endpoints related to port allocation, configuration,
and status monitoring.
"""

from fastapi import APIRouter, HTTPException, status

from src.olv_launcher.dependencies import PluginManagerDep
from src.olv_launcher.models import (
    PortConfigRequest,
    PortStatusResponse,
    PortConfigResponse,
    AvailablePortsResponse,
)

router = APIRouter()


@router.get(
    "/status",
    response_model=PortStatusResponse,
    summary="Get port status",
    description="Get current port allocation status including allocated and available ports",
)
async def get_port_status(manager: PluginManagerDep) -> PortStatusResponse:
    """Get current port allocation status"""
    status_data = manager.get_port_status()
    return PortStatusResponse(**status_data)


@router.post(
    "/configure",
    response_model=PortConfigResponse,
    summary="Configure port ranges",
    description="Configure port ranges for plugin allocation",
)
async def configure_ports(
    request: PortConfigRequest, manager: PluginManagerDep = None
) -> PortConfigResponse:
    """Configure port ranges for plugin allocation"""
    try:
        manager.configure_ports(request.port_ranges)
        status_data = manager.get_port_status()

        return PortConfigResponse(
            message="Port ranges configured successfully",
            port_ranges=request.port_ranges,
            status=PortStatusResponse(**status_data),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to configure ports: {e}",
        )


@router.get(
    "/available",
    response_model=AvailablePortsResponse,
    summary="Get available ports",
    description="Get list of currently available ports",
)
async def get_available_ports(manager: PluginManagerDep) -> AvailablePortsResponse:
    """Get list of available ports"""
    status_data = manager.get_port_status()
    available_ports = status_data["available_ports"]

    return AvailablePortsResponse(
        available_ports=available_ports, total_available=len(available_ports)
    )
