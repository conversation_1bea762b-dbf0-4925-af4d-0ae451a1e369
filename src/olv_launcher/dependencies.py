"""
Dependency injection functions for OLV Launcher.

This module provides dependency injection functions that are used throughout
the FastAPI application to ensure consistent access to shared resources.
"""

from typing import Annotated
from fastapi import Depends, HTTPException, status

from .plugin_manager import UnifiedPluginManager
from .workspace_manager import WorkspaceManager
from .exceptions import PluginManagerNotInitializedError


def get_plugin_manager() -> UnifiedPluginManager:
    """
    Get the global plugin manager instance.

    This dependency ensures that all endpoints that need the plugin manager
    have access to the initialized instance.

    Returns:
        UnifiedPluginManager: The global plugin manager instance

    Raises:
        PluginManagerNotInitializedError: If plugin manager is not initialized
    """
    from .server import plugin_manager

    if plugin_manager is None:
        raise PluginManagerNotInitializedError("Plugin manager not initialized")

    return plugin_manager


def get_workspace_manager() -> WorkspaceManager:
    """
    Get the global workspace manager instance.

    This dependency ensures that all endpoints that need the workspace manager
    have access to the initialized instance.

    Returns:
        WorkspaceManager: The global workspace manager instance

    Raises:
        PluginManagerNotInitializedError: If workspace manager is not initialized
    """
    from .server import workspace_manager

    if workspace_manager is None:
        raise PluginManagerNotInitializedError("Workspace manager not initialized")

    return workspace_manager


# Type aliases for dependency injection
PluginManagerDep = Annotated[UnifiedPluginManager, Depends(get_plugin_manager)]
WorkspaceManagerDep = Annotated[WorkspaceManager, Depends(get_workspace_manager)]
