"""
Port management for OLV Launcher.

This module provides port allocation and management capabilities for plugin services.
"""

import logging
from typing import List, Optional, Set, Dict

from .models import PortRange
from .exceptions import PortAllocationError

logger = logging.getLogger(__name__)


class PortManager:
    """Manages port allocation for plugin services"""

    def __init__(self, port_ranges: List[str]):
        """
        Initialize port manager with configurable port ranges.

        Args:
            port_ranges: List of port range strings like ["8001-8005", "12000-12010"]
        """
        self.ranges = self._parse_port_ranges(port_ranges)
        self.allocated_ports: Dict[str, int] = {}  # plugin_name -> port
        self.available_ports = self._build_available_ports()

    def _parse_port_ranges(self, port_ranges: List[str]) -> List[PortRange]:
        """Parse port range strings into PortRange objects"""
        ranges = []
        for range_str in port_ranges:
            try:
                if "-" in range_str:
                    start, end = map(int, range_str.split("-"))
                    if start > end:
                        raise ValueError(f"Invalid port range: {range_str}")
                    ranges.append(PortRange(start=start, end=end))
                else:
                    port = int(range_str)
                    if not (1 <= port <= 65535):
                        raise ValueError(f"Invalid port number: {port}")
                    ranges.append(PortRange(start=port, end=port))
            except ValueError as e:
                raise PortAllocationError(
                    f"Failed to parse port range '{range_str}': {e}"
                )
        return ranges

    def _build_available_ports(self) -> Set[int]:
        """Build set of all available ports from ranges"""
        ports = set()
        for port_range in self.ranges:
            ports.update(port_range.to_list())
        return ports

    def allocate_port(self, plugin_name: str) -> int:
        """Allocate a port for a plugin"""
        if plugin_name in self.allocated_ports:
            return self.allocated_ports[plugin_name]

        # Find first available port
        for port in sorted(self.available_ports):
            if port not in self.allocated_ports.values():
                self.allocated_ports[plugin_name] = port
                logger.info(f"Allocated port {port} to plugin {plugin_name}")
                return port

        raise PortAllocationError(f"No available ports for plugin {plugin_name}")

    def release_port(self, plugin_name: str) -> None:
        """Release a port allocated to a plugin"""
        if plugin_name in self.allocated_ports:
            port = self.allocated_ports.pop(plugin_name)
            logger.info(f"Released port {port} from plugin {plugin_name}")

    def get_port(self, plugin_name: str) -> Optional[int]:
        """Get the port allocated to a plugin"""
        return self.allocated_ports.get(plugin_name)

    def get_status(self) -> Dict:
        """Get port allocation status"""
        available_ports = []
        for port_range in self.ranges:
            for port in port_range.to_list():
                if port not in self.allocated_ports.values():
                    available_ports.append(port)

        return {
            "allocated_ports": self.allocated_ports.copy(),
            "available_ports": sorted(available_ports),
            "total_ranges": [str(r) for r in self.ranges],
        }
