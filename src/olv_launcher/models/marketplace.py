"""
Plugin marketplace models for OLV Launcher.

This module defines data models for the plugin marketplace system,
including marketplace plugins, installation status, and update information.
"""

from typing import List, Optional
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime


class MarketplacePluginStatus(str, Enum):
    """Marketplace plugin status enumeration"""
    AVAILABLE = "available"
    INSTALLED = "installed"
    UPDATE_AVAILABLE = "update_available"
    INSTALLING = "installing"
    UPDATING = "updating"
    INSTALL_FAILED = "install_failed"
    UPDATE_FAILED = "update_failed"


class InstallationStatus(str, Enum):
    """Plugin installation status enumeration"""
    PENDING = "pending"
    DOWNLOADING = "downloading"
    EXTRACTING = "extracting"
    INSTALLING_DEPS = "installing_deps"
    CONFIGURING = "configuring"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class MarketplacePluginInfo(BaseModel):
    """Information about a plugin available in the marketplace"""

    name: str = Field(..., description="Plugin name")
    service_type: str = Field(..., description="Service type (asr, tts, llm)")
    version: str = Field(..., description="Plugin version")
    description: str = Field(default="", description="Plugin description")
    author: str = Field(default="", description="Plugin author")
    repository_url: str = Field(default="", description="Plugin repository URL")
    download_url: str = Field(..., description="Download URL for the plugin")
    file_size: Optional[int] = Field(default=None, description="Plugin file size in bytes")
    dependencies: List[str] = Field(default_factory=list, description="Plugin dependencies")
    tags: List[str] = Field(default_factory=list, description="Plugin tags")
    license: str = Field(default="", description="Plugin license")
    created_at: Optional[datetime] = Field(default=None, description="Plugin creation date")
    updated_at: Optional[datetime] = Field(default=None, description="Plugin last update date")


    # Installation status
    status: MarketplacePluginStatus = Field(default=MarketplacePluginStatus.AVAILABLE, description="Plugin status")
    installed_version: Optional[str] = Field(default=None, description="Currently installed version")

    # Marketplace metadata
    marketplace_url: str = Field(..., description="Marketplace source URL")
    plugin_path: str = Field(..., description="Path within the marketplace repository")


class PluginInstallationProgress(BaseModel):
    """Plugin installation progress information"""

    plugin_name: str = Field(..., description="Plugin name")
    status: InstallationStatus = Field(..., description="Installation status")
    progress_percentage: float = Field(default=0.0, description="Progress percentage (0-100)")
    current_step: str = Field(default="", description="Current installation step")
    total_steps: int = Field(default=1, description="Total number of steps")
    current_step_index: int = Field(default=0, description="Current step index")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    started_at: datetime = Field(default_factory=datetime.now, description="Installation start time")
    completed_at: Optional[datetime] = Field(default=None, description="Installation completion time")
    download_size: Optional[int] = Field(default=None, description="Total download size")
    downloaded_size: Optional[int] = Field(default=None, description="Downloaded size")


class MarketplaceInfo(BaseModel):
    """Information about a plugin marketplace"""

    name: str = Field(..., description="Marketplace name")
    url: str = Field(..., description="Marketplace URL")
    branch: str = Field(default="main", description="Repository branch")
    description: str = Field(default="", description="Marketplace description")
    last_updated: Optional[datetime] = Field(default=None, description="Last update time")
    plugin_count: int = Field(default=0, description="Number of plugins")
    is_default: bool = Field(default=False, description="Whether this is the default marketplace")
    is_enabled: bool = Field(default=True, description="Whether this marketplace is enabled")


class PluginUpdateInfo(BaseModel):
    """Information about plugin updates"""

    plugin_name: str = Field(..., description="Plugin name")
    current_version: str = Field(..., description="Currently installed version")
    latest_version: str = Field(..., description="Latest available version")
    update_available: bool = Field(..., description="Whether an update is available")
    changelog: str = Field(default="", description="Update changelog")
    update_size: Optional[int] = Field(default=None, description="Update size in bytes")
    is_breaking_change: bool = Field(default=False, description="Whether this is a breaking change")
    required_dependencies: List[str] = Field(default_factory=list, description="Required dependencies for update")


class MarketplaceSearchFilter(BaseModel):
    """Search and filter options for marketplace plugins"""

    query: Optional[str] = Field(default=None, description="Search query")
    service_type: Optional[str] = Field(default=None, description="Filter by service type")
    tags: List[str] = Field(default_factory=list, description="Filter by tags")
    author: Optional[str] = Field(default=None, description="Filter by author")
    limit: int = Field(default=50, description="Maximum number of results")
    offset: int = Field(default=0, description="Result offset for pagination")


class MarketplaceCache(BaseModel):
    """Cache information for marketplace data"""

    marketplace_url: str = Field(..., description="Marketplace URL")
    cached_at: datetime = Field(default_factory=datetime.now, description="Cache timestamp")
    expires_at: datetime = Field(..., description="Cache expiration time")
    plugins: List[MarketplacePluginInfo] = Field(default_factory=list, description="Cached plugins")
    etag: Optional[str] = Field(default=None, description="ETag for cache validation")
