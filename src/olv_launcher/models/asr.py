from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from src.olv_launcher.models.api import PluginStatus


class TranscribeRequest(BaseModel):
    """Request model for transcription with plugin configuration"""

    audio: str = Field(
        ..., description="Base64 encoded audio data (16-bit PCM, 16kHz)"
    )
    plugin_config: Dict[str, Any] = Field(
        ..., description="Plugin configuration data"
    )
    custom: Optional[Dict[str, Any]] = Field(
        None, description="Optional custom parameters that will be passed to the ASR engine"
    )


class TranscribeResponse(BaseModel):
    """Response model for transcription"""

    text: str = Field(..., description="Transcribed text")
    processing_time: float = Field(..., description="Processing time in seconds")
    instance_id: str = Field(..., description="Engine instance ID used for transcription")


class HealthResponse(BaseModel):
    """Health check response"""

    status: PluginStatus = Field(..., description="Plugin status")
