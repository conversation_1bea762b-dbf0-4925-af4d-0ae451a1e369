"""
Plugin type definitions for OLV Launcher.
"""

import json
from pathlib import Path
from pydantic import BaseModel, Field, computed_field
from typing import Dict, Any, Optional
from urllib.parse import urlparse

from .service import ServiceType
from ..exceptions import PluginConfigurationError, InvalidServiceTypeError


class PluginConfig(BaseModel):
    """Plugin configuration model"""

    version: str = Field(..., description="Plugin version")
    service_type: str = Field(..., description="Service type")
    description: str = Field(default="", description="Plugin description")
    author: str = Field(default="", description="Plugin author")
    service_url: Optional[str] = Field(default=None, description="Service URL (for remote plugins) or localhost for local plugins")
    package_manager: Optional[str] = Field(default="uv", description="Package manager for local plugins")
    plugin_json_schema: Optional[Dict[str, Any]] = Field(
        default=None, description="JSON schema for plugin configuration"
    )
    plugin_ui_schema: Optional[Dict[str, Any]] = Field(
        default=None, description="UI schema for plugin configuration"
    )


class PluginInfo(BaseModel):
    """Information about a discovered plugin"""

    name: str = Field(..., description="Plugin name")
    config_path: Optional[Path] = Field(default=None, description="Path to plugin.json (for local plugins)")
    config: PluginConfig = Field(..., description="Plugin configuration")

    @computed_field
    @property
    def plugin_dir(self) -> Optional[Path]:
        """Get the plugin directory path (only for local plugins)"""
        return self.config_path.parent if self.config_path else None

    @computed_field
    @property
    def version(self) -> str:
        """Get plugin version"""
        return self.config.version

    @computed_field
    @property
    def description(self) -> str:
        """Get plugin description"""
        return self.config.description

    @computed_field
    @property
    def author(self) -> str:
        """Get plugin author"""
        return self.config.author

    @computed_field
    @property
    def service_type(self) -> ServiceType:
        """Get service type as enum"""
        try:
            return ServiceType(self.config.service_type)
        except ValueError as e:
            raise InvalidServiceTypeError(f"Invalid service type in {self.name}: {e}")

    @computed_field
    @property
    def service_url(self) -> Optional[str]:
        """Get service URL"""
        return self.config.service_url

    @computed_field
    @property
    def is_local(self) -> bool:
        """Check if plugin is local"""
        if not self.config.service_url:
            return True  # No service_url means local

        # Parse URL to check if it's localhost
        parsed = urlparse(self.config.service_url)
        local_hosts = {'localhost', '127.0.0.1', '0.0.0.0', '::1'}
        return parsed.hostname in local_hosts

    @computed_field
    @property
    def is_remote(self) -> bool:
        """Check if plugin is remote"""
        return not self.is_local

    @classmethod
    def from_config_file(cls, name: str, config_path: Path) -> "PluginInfo":
        """Create PluginInfo from a plugin.json file"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            raise PluginConfigurationError(
                f"Failed to load plugin config for {name}: {e}"
            )

        try:
            config = PluginConfig(**config_data)
        except Exception as e:
            raise PluginConfigurationError(
                f"Invalid plugin configuration for {name}: {e}"
            )

        return cls(name=name, config_path=config_path, config=config)

    @classmethod
    def from_config_data(cls, name: str, config_data: Dict[str, Any]) -> "PluginInfo":
        """Create PluginInfo from configuration data (for remote plugins)"""
        try:
            config = PluginConfig(**config_data)
        except Exception as e:
            raise PluginConfigurationError(
                f"Invalid plugin configuration for {name}: {e}"
            )

        return cls(name=name, config_path=None, config=config)

    def get_schemas(self) -> Dict[str, Dict[str, Any]]:
        """Get JSON and UI schemas from plugin configuration"""
        schemas = {}

        if self.config.plugin_json_schema:
            schemas["plugin_json_schema"] = self.config.plugin_json_schema
        else:
            schemas["plugin_json_schema"] = {}

        if self.config.plugin_ui_schema:
            schemas["plugin_ui_schema"] = self.config.plugin_ui_schema
        else:
            schemas["plugin_ui_schema"] = {}

        return schemas

    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for creating instances"""
        if (
            self.config.plugin_json_schema
            and isinstance(self.config.plugin_json_schema, dict)
            and "default" in self.config.plugin_json_schema
        ):
            return self.config.plugin_json_schema["default"]
        return {}

    def get_full_service_url(self, port: Optional[int] = None) -> str:
        """Get complete service URL for the plugin"""
        if self.is_remote:
            return self.service_url
        elif self.service_url:
            # Local plugin with explicit service_url (like localhost:8080)
            return self.service_url
        elif port:
            # Local plugin with allocated port
            return f"http://127.0.0.1:{port}"
        else:
            raise ValueError("Local plugin requires either service_url or port to generate service URL")
