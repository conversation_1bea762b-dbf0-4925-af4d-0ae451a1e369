"""
Pydantic models for OLV Launcher API.

This module defines request and response models used throughout the FastAPI
application to ensure proper data validation and serialization.
"""

from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime


# Status Enums
class PluginStatus(str, Enum):
    """Plugin service status enumeration"""
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"
    STARTING = "starting"
    STOPPING = "stopping"


# Request Models
class PortConfigRequest(BaseModel):
    """Request model for configuring port ranges"""

    port_ranges: List[str] = Field(
        ...,
        description="List of port ranges in format 'start-end' or single ports",
        example=["8001-8005", "12000-12010", "9000"],
    )


class PluginInstanceCreateRequest(BaseModel):
    """Request model for creating a plugin instance"""

    config: Dict[str, Any] = Field(
        ..., description="Complete configuration for the plugin instance matching the plugin's JSON schema"
    )


class ErrorResponse(BaseModel):
    """Error response model"""

    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Human-readable error message")
    detail: Optional[str] = Field(None, description="Detailed error information")


class PluginApiInfo(BaseModel):
    """Plugin information model for API responses"""

    name: str = Field(..., description="Plugin name")
    service_type: str = Field(..., description="Service type")
    version: str = Field(..., description="Plugin version")
    description: str = Field(..., description="Plugin description")
    author: str = Field(..., description="Plugin author")
    status: PluginStatus = Field(..., description="Plugin service status")
    service_url: Optional[str] = Field(None, description="Complete service URL for accessing the plugin")
    is_local: bool = Field(..., description="Whether the plugin is local or remote")


class PluginListResponse(BaseModel):
    """Response model for listing plugins"""

    plugins: List[PluginApiInfo] = Field(..., description="List of plugins")


class ServicePluginListResponse(BaseModel):
    """Response model for listing plugins by service type"""

    service_type: str = Field(..., description="Service type")
    plugins: List[PluginApiInfo] = Field(..., description="List of plugins")


class PluginStartResponse(BaseModel):
    """Response model for starting a plugin"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    service_url: str = Field(..., description="Service URL")
    status: PluginStatus = Field(..., description="Plugin status")


class PluginStopResponse(BaseModel):
    """Response model for stopping a plugin"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    status: PluginStatus = Field(..., description="Plugin status")


class PluginHealthResponse(BaseModel):
    """Response model for plugin health check"""

    plugin_name: str = Field(..., description="Plugin name")
    status: PluginStatus = Field(..., description="Plugin status")
    service_url: Optional[str] = Field(None, description="Service URL if available")
    http_status: Optional[int] = Field(None, description="HTTP status code")
    error: Optional[str] = Field(None, description="Error message if any")
    message: Optional[str] = Field(None, description="Additional message")


class PluginSchemasResponse(BaseModel):
    """Response model for all plugin schemas"""

    plugin_name: str = Field(..., description="Plugin name")
    schemas: Dict[str, Dict] = Field(
        ...,
        description="All plugin configuration schemas including JSON and UI schemas",
    )


class PluginSchemaResponse(BaseModel):
    """Response model for plugin schemas"""

    plugin_name: str = Field(..., description="Plugin name")
    json_schema: Dict = Field(..., description="JSON schema for plugin configuration")
    ui_schema: Dict = Field(..., description="UI schema for plugin configuration")


class PortStatusResponse(BaseModel):
    """Response model for port status"""

    allocated_ports: Dict[str, int] = Field(
        ..., description="Currently allocated ports"
    )
    available_ports: List[int] = Field(..., description="Available ports")
    total_ranges: List[str] = Field(..., description="Configured port ranges")


class PortConfigResponse(BaseModel):
    """Response model for port configuration"""

    message: str = Field(..., description="Success message")
    port_ranges: List[str] = Field(..., description="Configured port ranges")
    status: PortStatusResponse = Field(..., description="Current port status")


class AvailablePortsResponse(BaseModel):
    """Response model for available ports"""

    available_ports: List[int] = Field(..., description="List of available ports")
    total_available: int = Field(..., description="Total number of available ports")


class PluginLogsResponse(BaseModel):
    """Response model for plugin logs"""

    plugin_name: str = Field(..., description="Plugin name")
    logs: List[str] = Field(..., description="List of log lines")
    total_lines: int = Field(..., description="Total number of log lines returned")
    has_more: bool = Field(..., description="Whether there are more logs available")


class PluginLogsClearResponse(BaseModel):
    """Response model for clearing plugin logs"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")


class PluginForceCleanupResponse(BaseModel):
    """Response model for force cleanup of plugin"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    status: str = Field(..., description="Plugin status after cleanup")


class PluginInstanceCreateResponse(BaseModel):
    """Response model for creating a plugin instance"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    instance_id: str = Field(..., description="Created instance ID")
    config: Dict[str, Any] = Field(..., description="Applied configuration")


class PluginInstanceListResponse(BaseModel):
    """Response model for listing plugin instances"""

    plugin_name: str = Field(..., description="Plugin name")
    instances: Dict[str, Any] = Field(..., description="Dictionary of instance information")


class PluginInstanceDeleteResponse(BaseModel):
    """Response model for deleting a plugin instance"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    instance_id: str = Field(..., description="Deleted instance ID")


# Marketplace API Models
class MarketplacePluginApiInfo(BaseModel):
    """Marketplace plugin information for API responses"""

    name: str = Field(..., description="Plugin name")
    service_type: str = Field(..., description="Service type")
    version: str = Field(..., description="Plugin version")
    description: str = Field(..., description="Plugin description")
    author: str = Field(..., description="Plugin author")
    repository_url: str = Field(..., description="Plugin repository URL")
    download_url: str = Field(..., description="Download URL for the plugin")
    file_size: Optional[int] = Field(None, description="Plugin file size in bytes")
    dependencies: List[str] = Field(default_factory=list, description="Plugin dependencies")
    tags: List[str] = Field(default_factory=list, description="Plugin tags")
    license: str = Field(default="", description="Plugin license")
    created_at: Optional[datetime] = Field(None, description="Plugin creation date")
    updated_at: Optional[datetime] = Field(None, description="Plugin last update date")
    download_count: int = Field(default=0, description="Download count")
    rating: Optional[float] = Field(None, description="Plugin rating")
    status: str = Field(..., description="Plugin status (available, installed, update_available)")
    installed_version: Optional[str] = Field(None, description="Currently installed version")


class MarketplaceSearchRequest(BaseModel):
    """Request model for marketplace plugin search"""

    query: Optional[str] = Field(None, description="Search query")
    service_type: Optional[str] = Field(None, description="Filter by service type")
    tags: List[str] = Field(default_factory=list, description="Filter by tags")
    author: Optional[str] = Field(None, description="Filter by author")
    min_rating: Optional[float] = Field(None, description="Minimum rating filter")
    sort_by: str = Field(default="name", description="Sort field")
    sort_order: str = Field(default="asc", description="Sort order")
    limit: int = Field(default=50, description="Maximum number of results")
    offset: int = Field(default=0, description="Result offset for pagination")


class MarketplacePluginListResponse(BaseModel):
    """Response model for marketplace plugin list"""

    plugins: List[MarketplacePluginApiInfo] = Field(..., description="List of marketplace plugins")
    total_count: int = Field(..., description="Total number of plugins")
    has_more: bool = Field(..., description="Whether there are more results")


class PluginInstallRequest(BaseModel):
    """Request model for plugin installation"""

    plugin_name: str = Field(..., description="Name of the plugin to install")


class PluginInstallResponse(BaseModel):
    """Response model for plugin installation"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    status: str = Field(..., description="Installation status")
    progress_id: str = Field(..., description="Progress tracking ID")


class PluginInstallProgressResponse(BaseModel):
    """Response model for installation progress"""

    plugin_name: str = Field(..., description="Plugin name")
    status: str = Field(..., description="Installation status")
    progress_percentage: float = Field(..., description="Progress percentage")
    current_step: str = Field(..., description="Current installation step")
    total_steps: int = Field(..., description="Total number of steps")
    current_step_index: int = Field(..., description="Current step index")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    started_at: datetime = Field(..., description="Installation start time")
    completed_at: Optional[datetime] = Field(None, description="Installation completion time")


class PluginUninstallRequest(BaseModel):
    """Request model for plugin uninstallation"""

    plugin_name: str = Field(..., description="Name of the plugin to uninstall")


class PluginUninstallResponse(BaseModel):
    """Response model for plugin uninstallation"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")


class PluginUpdateCheckResponse(BaseModel):
    """Response model for plugin update check"""

    plugin_name: str = Field(..., description="Plugin name")
    current_version: str = Field(..., description="Currently installed version")
    latest_version: str = Field(..., description="Latest available version")
    update_available: bool = Field(..., description="Whether an update is available")
    changelog: str = Field(default="", description="Update changelog")


class PluginUpdateRequest(BaseModel):
    """Request model for plugin update"""

    plugin_name: str = Field(..., description="Name of the plugin to update")


class PluginUpdateResponse(BaseModel):
    """Response model for plugin update"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    old_version: str = Field(..., description="Previous version")
    new_version: str = Field(..., description="New version")
    progress_id: str = Field(..., description="Progress tracking ID")


class MarketplaceRefreshResponse(BaseModel):
    """Response model for marketplace refresh"""

    message: str = Field(..., description="Success message")
    plugin_count: int = Field(..., description="Number of plugins found")
    last_updated: datetime = Field(..., description="Last update timestamp")
