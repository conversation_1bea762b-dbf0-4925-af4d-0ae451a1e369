"""
Configuration management for OLV Launcher.

This module provides configuration classes and settings management
using Pydantic BaseSettings for environment variable support.
Configuration can be loaded from environment variables or .env file.
"""

from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class LauncherSettings(BaseSettings):
    """
    Main configuration settings for OLV Launcher.

    Settings are loaded from:
    1. Environment variables with prefix 'OLV_LAUNCHER_'
    2. .env file in the project root
    3. Default values defined here
    """

    model_config = SettingsConfigDict(
        env_prefix="OLV_LAUNCHER_",
        case_sensitive=False,
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",
    )

    # Server settings
    host: str = Field(default="127.0.0.1", description="Server host address")
    port: int = Field(default=7000, description="Server port number", ge=1024, le=65535)
    reload: bool = Field(default=False, description="Enable auto-reload in development")

    # Plugin settings
    plugins_dir: str = Field(
        default="plugins", description="Directory containing plugins"
    )
    default_port_ranges: List[str] = Field(
        default=["8001-8020", "9001-9020", "10001-10020"],
        description="Default port ranges for plugin allocation",
    )

    # Service settings
    health_check_interval: int = Field(
        default=30, description="Health check interval in seconds", ge=5
    )
    plugin_startup_timeout: int = Field(
        default=600, description="Timeout for plugin startup in seconds", ge=30
    )
    plugin_health_timeout: int = Field(
        default=10, description="Timeout for plugin health checks in seconds", ge=1
    )

    # Logging settings
    log_level: str = Field(
        default="INFO",
        description="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)",
    )
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string",
    )

    # CORS settings
    cors_origins: List[str] = Field(default=["*"], description="Allowed CORS origins")
    cors_credentials: bool = Field(
        default=True, description="Allow credentials in CORS"
    )
    cors_methods: List[str] = Field(default=["*"], description="Allowed CORS methods")
    cors_headers: List[str] = Field(default=["*"], description="Allowed CORS headers")

    # Environment sync settings
    enable_uv_sync: bool = Field(
        default=True, description="Enable automatic UV environment synchronization"
    )
    uv_sync_timeout: int = Field(
        default=300, description="Timeout for UV sync in seconds", ge=60
    )


# Global settings instance
settings = LauncherSettings()
