"""
Port management type definitions for OLV Launcher.
"""

from typing import List
from pydantic import BaseModel, Field, model_validator


class PortRange(BaseModel):
    """Represents a range of ports for allocation"""

    start: int = Field(..., ge=1, le=65535, description="Start port number")
    end: int = Field(..., ge=1, le=65535, description="End port number")

    @model_validator(mode="after")
    def validate_range(self):
        """Validate that start <= end"""
        if self.start > self.end:
            raise ValueError(f"Start port {self.start} must be <= end port {self.end}")
        return self

    def __contains__(self, port: int) -> bool:
        """Check if a port is within this range"""
        return self.start <= port <= self.end

    def to_list(self) -> List[int]:
        """Convert range to list of ports"""
        return list(range(self.start, self.end + 1))

    def __str__(self) -> str:
        """String representation of the port range"""
        if self.start == self.end:
            return str(self.start)
        return f"{self.start}-{self.end}"
