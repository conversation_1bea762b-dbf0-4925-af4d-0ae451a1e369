"""
Custom middleware for OLV Launcher.

This module provides custom middleware for logging, request tracking,
and other cross-cutting concerns.
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to log incoming requests and responses.

    This middleware logs request information including method, URL,
    processing time, and response status.
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process incoming request and log request/response information.

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or route handler

        Returns:
            Response: The HTTP response
        """
        start_time = time.time()

        # Log incoming request
        logger.info(
            f"Incoming request: {request.method} {request.url.path} "
            f"from {request.client.host if request.client else 'unknown'}"
        )

        try:
            # Process request
            response = await call_next(request)

            # Calculate processing time
            process_time = time.time() - start_time

            # Log response
            logger.info(
                f"Request completed: {request.method} {request.url.path} "
                f"- Status: {response.status_code} - Time: {process_time:.3f}s"
            )

            # Add processing time to response headers
            response.headers["X-Process-Time"] = str(process_time)

            return response

        except Exception as e:
            # Log errors
            process_time = time.time() - start_time
            logger.error(
                f"Request failed: {request.method} {request.url.path} "
                f"- Error: {str(e)} - Time: {process_time:.3f}s"
            )
            raise


class RequestTrackingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to track request statistics.

    This middleware can be used to collect metrics about API usage,
    response times, and error rates.
    """

    def __init__(self, app):
        super().__init__(app)
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Track request statistics.

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or route handler

        Returns:
            Response: The HTTP response
        """
        start_time = time.time()
        self.request_count += 1

        try:
            response = await call_next(request)

            # Track response time
            response_time = time.time() - start_time
            self.total_response_time += response_time

            # Track errors
            if response.status_code >= 400:
                self.error_count += 1

            return response

        except Exception as e:
            self.error_count += 1
            response_time = time.time() - start_time
            self.total_response_time += response_time
            raise

    def get_stats(self) -> dict:
        """
        Get current request statistics.

        Returns:
            dict: Statistics including request count, error count, and average response time
        """
        avg_response_time = (
            self.total_response_time / self.request_count
            if self.request_count > 0
            else 0
        )

        return {
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "error_rate": self.error_count / self.request_count
            if self.request_count > 0
            else 0,
            "average_response_time": avg_response_time,
        }
