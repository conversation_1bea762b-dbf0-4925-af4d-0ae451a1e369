# OLV Launcher Environment Configuration File
# Copy this file as .env and modify the configuration as needed

# ============== Server Settings ==============
# Server listening address (Default: 127.0.0.1)
# Use 0.0.0.0 to allow access from external devices
OLV_LAUNCHER_HOST=127.0.0.1

# Server port number (Default: 7000)
# Range: 1024-65535
OLV_LAUNCHER_PORT=7000

# Development mode auto-reload (Default: false)
# Enable only in development environment
OLV_LAUNCHER_RELOAD=false

# ============== Plugin Settings ==============
# Plugin directory path (Default: plugins)
# Path relative to the project root directory
OLV_LAUNCHER_PLUGINS_DIR=plugins

# Default port ranges (Default: ["8001-8020", "9001-9020", "10001-10020"])
# List of port ranges for plugin port allocation, separated by commas
OLV_LAUNCHER_DEFAULT_PORT_RANGES=8001-8020,9001-9020,10001-10020

# ============== Service Settings ==============
# Health check interval (Default: 30 seconds)
# Minimum value: 5 seconds
OLV_LAUNCHER_HEALTH_CHECK_INTERVAL=30

# Plugin startup timeout (Default: 600 seconds)
# Minimum value: 30 seconds
OLV_LAUNCHER_PLUGIN_STARTUP_TIMEOUT=600

# Plugin health check timeout (Default: 10 seconds)
# Minimum value: 1 second
OLV_LAUNCHER_PLUGIN_HEALTH_TIMEOUT=10

# ============== Logging Settings ==============
# Log level (Default: INFO)
# Available values: DEBUG, INFO, WARNING, ERROR, CRITICAL
OLV_LAUNCHER_LOG_LEVEL=INFO

# Log format (Default: %(asctime)s - %(name)s - %(levelname)s - %(message)s)
# Python logging format string
OLV_LAUNCHER_LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ============== CORS Settings ==============
# Allowed CORS origins (Default: ["*"])
# Separate multiple origins with commas, * means allow all origins
OLV_LAUNCHER_CORS_ORIGINS=*

# Allow CORS credentials (Default: true)
OLV_LAUNCHER_CORS_CREDENTIALS=true

# Allowed CORS methods (Default: ["*"])
# Separate multiple methods with commas, * means allow all methods
OLV_LAUNCHER_CORS_METHODS=*

# Allowed CORS headers (Default: ["*"])
# Separate multiple headers with commas, * means allow all headers
OLV_LAUNCHER_CORS_HEADERS=*

# ============== Environment Sync Settings ==============
# Enable UV environment sync (Default: true)
# Automatically synchronize Python environment dependencies
OLV_LAUNCHER_ENABLE_UV_SYNC=true

# UV sync timeout (Default: 300 seconds)
# Minimum value: 60 seconds
OLV_LAUNCHER_UV_SYNC_TIMEOUT=300

# ============== UI Configuration (olv-launcher-ui) ==============
# Backend API base URL
# Used for the frontend to connect to the OLV Launcher backend
NEXT_PUBLIC_API_BASE_URL=http://localhost:7000