---
description: 
globs: 
alwaysApply: false
---
功能需求 (Features / Functional Requirements)

1. 角色 (Character)
定义: AI角色及其相关属性和管理功能

1.1 角色定义 (Character Definition):
- 1.1.0 角色唯一标识符 (Character ID): 系统为每个角色生成的唯一ID。
- 1.1.1 角色展示名称 (Display Name): 用户在界面上看到的角色名称，对应 {{char}} 宏。
- 1.1.2 角色介绍 (Character Introduction): 用于向用户展示的角色梗概。
- 1.1.3 角色相关宏 (Macros):
  - 角色名 (Character Name - {{char}})
  - 角色核心提示词 (Character Main Prompt - {{main}})
  - 示例对话 (Example Chats - {{mesExamples}}): 
    - 可以设置为临时 (N 条 AI 消息后删除) 或永久。
<START>
{{user}}: (user's turn)
{{char}}: (char's turn)
<START>
{{user}}: (user's turn)
{{char}}: (char's turn)
  - 角色性格 (Character Personality - {{personality}})
  - 作者备注宏 (Author's Note - {{authorNote}})
- 1.1.4 角色系统提示词 (Character System Prompt - {System}):
  - 根据角色相关宏、用户相关宏和世界相关宏，系统根据"系统提示词模板"自动格式化得到最终的系统提示词。
  - 用户可以修改系统提示词模板。
- 1.1.5 头像 (Profile Picture / Headshot):
  - 一张代表角色的静态图片URL，主要用于UI中（如角色列表、聊天窗口头像等）的快速识别。
  - 用户可以上传图片并选择头像区域。
- 1.1.6 角色形象 (Character Image):
  - Live2D:
    - 可以通过URL设置角色的 Live2D 模型文件 (.model3.json)。
    - 可以调整Live2D模型的显示比例以及初始的X、Y轴位置偏移。
    - 可以配置特定标签 (Tag) 到 Live2D 的特定表情 (live2d_expression) 或动作 (live2d_motion) 的映射。可以指定表情/动作的名称或索引。
  - (Future) 单张/多张静态图片。
- 1.1.7 关联故事书/世界书 (Linked Storybooks/Lorebooks):
  - 角色可以直接关联一个或多个"故事书"或"世界书" (Lorebook)。
  - 对于每个关联的世界书，可以独立设置其是否在此角色上启用，以及在内容匹配时递归激活的最大深度。
- 1.1.10 文字转语音 (TTS):
  - 关联的TTS（文本转语音）配置，如选择的音色、语速、音调等。
  - 允许用户创建角色声音（例如通过快速克隆技术）。
  - (注: 具体的TTS配置模型细节待定)
- 1.1.12 创建者信息 (Creator Info): 记录角色的创建者用户ID (owner_id)。
- 1.1.13 可见性/分享设置 (Visibility/Sharing Settings):
  - 私密 (Private): 仅创建者可见和可编辑。
  - 不公开链接 (Unlisted): 拥有链接的用户可以查看，但不会在公开列表中展示。
  - 公开 (Public): 对所有用户可见，并且如果设置允许，可以被其他用户"派生"(Fork)。
- 1.1.14 创建时间 (Creation Timestamp): 角色创建时的时间戳。
- 1.1.14a 更新时间 (Last Updated Timestamp): 角色信息最后修改的时间戳。
- 1.1.15 标签 (Tags): 用户为角色定义的描述性标签列表。
- 1.1.16 统计数据 (Statistics):
  - 浏览次数 (Views Count)
  - 点赞次数 (Like Count)
  - 点踩次数 (Dislike Count)
  - 收藏次数 (Star Count / Favorite Count)
  - 派生次数 (Fork Count)
- 1.1.17 第一条消息 (First Message): 角色在对话开始时发送的默认第一条消息。
- 1.1.18 (更新) 作者备注 (Author's Note):
  - 一段文本内容，可以通过 {{authorNote}} 宏在角色提示词中动态使用。
  - 用户可以选择此备注是否在对话过程中自动插入。
  - 如果选择自动插入，可以配置其插入频率（例如，在每N条用户消息之后插入）。
- 1.1.19 派生来源 (Forked From): 如果此角色是通过"派生"(Fork)操作创建的，此处记录源角色的ID。
- 1.1.20 版本号 (Version): 用于数据结构和功能迭代的版本控制。

3. 世界系统 (World System)
定义: "世界系统"允许用户为AI角色定义一个特定的交互环境、背景设定、规则和世界书。AI在不同"世界"中的行为可以被该世界的设定所影响。

3.1 世界 (World):
- 3.1.1 用户可以创建、编辑和删除"世界"。
- 3.1.2 每个世界拥有：
  - 唯一标识符 (World ID): 系统为每个世界生成的唯一ID。
  - 世界名称 (World Name): 用户定义的世界名称，对应 {{world}} 宏。
  - 世界描述 (World Description): 向用户展示的关于此世界的详细介绍 (必填)。
  - 世界背景信息 (World Background Information): 定义世界的上下文和背景设定，对应 {{world_background}} 宏。
  - 所有者ID (Owner ID): 创建此世界的用户ID。
  - 关联的用户人格ID (Associated User Persona ID): 指定在此世界中默认使用的用户人格。
  - 关联的世界书 (Linked Lorebooks):
    - 世界可以关联一个或多个"世界书" (Lorebook)。
    - 对于每个关联的世界书，可以独立设置其是否在此世界中启用，以及在内容匹配时递归激活的最大深度。
  - 可见性 (Visibility): 世界的可见性设置 (私密 Private, 不公开链接 Unlisted, 公开 Public)。
  - 标签 (Tags): 用户为世界定义的描述性标签列表。
  - 创建时间 (Creation Timestamp): 世界创建时的时间戳。
  - 更新时间 (Last Updated Timestamp): 世界信息最后修改的时间戳。
  - (可选) 派生来源世界ID (Forked From World ID): 如果此世界是派生而来的，记录源世界的ID。
  - 版本号 (Version): 用于数据结构和功能迭代的版本控制。
- 3.1.3 AI角色参与 (AI Character Participation):
  - 用户可以将一个或多个已拥有的AI角色"添加"到某个世界中。
  - 同一个AI角色可以参与多个不同的世界。
  - 可以为每个参与此世界的AI角色进行特定配置：
    - 是否允许该世界中的交互影响此AI角色的长期记忆。
    - (可选) 为此AI角色在该世界上下文中设定特定的身份、行为指示或提示词调整。

3.2 世界书系统 (Lore System / Storybook System)
定义: 世界书是用于组织和管理背景信息、世界设定、角色知识、剧情点等内容的系统。这些信息可以动态地注入到AI的上下文中，以影响其行为和响应。

- 3.2.1 世界书 (Lorebook / Storybook):
  - 世界书是"世界书条目"的结构化集合。
  - 每个世界书拥有：
    - 唯一标识符 (Lorebook ID)。
    - 世界书名称 (Name): 用户可读的名称，例如"霍格沃茨的魔法规则"，"赛博坦星球历史"。
    - (可选) 描述 (Description): 关于此世界书内容或用途的补充说明。
    - (可选) 所有者ID (Owner ID): 记录世界书的创建者。
    - 创建与更新时间戳 (Creation and Update Timestamps)。
    - 版本号 (Version)。
  - 世界书可以被角色直接关联，也可以被世界关联，以决定其作用范围。

- 3.2.2 世界书条目 (Lore Entry):
  - 每个世界书包含一个或多个"世界书条目"，每个条目代表一条具体的知识或信息。
  - 条目属性:
    - 唯一标识符 (Entry ID)。
    - 是否启用 (Enabled): 控制此条目当前是否有效并参与激活判断。
    - 激活策略与条件 (Activation Strategy & Conditions): 定义条目内容如何以及何时被触发并注入上下文。
      - 主要激活策略 (Activation Strategy):
        - 始终注入 (Always Insert): 条目内容作为核心设定，总是在AI的上下文中（适用于此世界书生效的场景）。
        - 基于条件过滤触发 (Filter-Based Trigger): 仅当满足一组预定义的、复杂的过滤条件时，条目内容才被激活和注入。
      - 过滤条件 (Filter Conditions): (当主要激活策略为"基于条件过滤触发"时使用)
        - 基本条件类型包括：
          - 精确关键词匹配 (Keyword Exact Match): 对话内容中出现一个或多个精确的关键词。可配置是否区分大小写。
          - 正则表达式匹配 (Keyword Regex Match): 对话内容满足指定的正则表达式模式。
          - (Future) 语义相似度匹配 (Vector Similarity Match): 对话内容与条目定义的某段文本在语义上达到一定相似度阈值。
        - 逻辑组合 (Logical Grouping): 基本条件可以使用逻辑操作符（如 AND 所有条件满足, OR 任一条件满足, NOT 条件不满足）进行组合，形成可嵌套的复杂激活规则。
    - 条目内容 (Entry Content / Injected Text): 当条目被成功激活时，其实际注入到AI上下文（通常是Prompt）中的文本信息。内容可以是背景描述、角色行为指导、特定知识片段等。
    - 插入优先级 (Insertion Order / Priority): 数值型。当多个世界书条目同时被激活时，此数值决定了它们在Prompt中出现的顺序或哪个优先被考虑（通常数字越小优先级越高）。
    - 插入概率 (Insertion Probability): 0% 到 100% 的概率值。即使条目被激活，也会根据此概率决定是否真的将其内容注入上下文，用于增加多样性或随机性。
    - 持续效果 (Duration / Active Turns): （可选）条目内容在激活后，在AI的上下文中保持有效的对话轮数。留空则表示仅当轮生效或直至被其他逻辑覆盖。
    - 冷却时间 (Cooldown / Inactive Turns): （可选）条目在激活并失效（如持续效果结束）后，需要等待多少对话轮数才能被再次激活。
    - 扫描深度 (Scan Depth / Message History Scan): 定义系统在分析激活条件时，应回溯查看最近多少条对话消息（用户和AI的每条消息均计为一条）。
    - (可选) 备注 (Notes): 用户为条目添加的内部注释或说明文字。
    - 创建与更新时间戳 (Creation and Update Timestamps)。

- 3.2.3 世界书的递归激活 (Recursive Activation for Lorebooks):
  - 当一个世界书通过角色或世界配置启用时，可以为其设置最大递归激活深度。
  - 若启用递归激活（深度大于0），当一个世界书条目的"条目内容"被成功注入后，如果该注入的内容本身又触发了其他（来自任何已启用的、在扫描范围内的世界书的）条目的激活条件，则可以链式激活那些新条目。
  - 最大递归深度限制了这种链式激活的层数，以防止无限循环和过度复杂的上下文生成。

- 3.2.4 世界书与条目管理 (Lorebook and Entry Management):
  - 用户界面应提供方便的功能来创建、查看、编辑和删除世界书。
  - 在每个世界书内部，用户可以增、删、改、查各个世界书条目，并配置其所有属性。
  - (Future) 提供世界书及其条目的导入导出功能（例如，使用标准化的JSON或CSV格式）。

3.3 世界系统与AI交互逻辑 (World System & AI Interaction Logic)
- 当用户与某个AI角色进行交互时，如果该交互发生在特定的"世界"中，或者角色本身关联了"世界书"：
  - 系统会实时分析当前对话上下文（包括最新的用户输入和近期的对话历史）。
  - 根据已启用的世界书中的条目所定义的激活策略、过滤条件、优先级、概率、持续效果、冷却时间、扫描深度等设置，动态选择合适的条目内容组合。
  - 这些被选中的条目内容将被整合到AI生成响应所需的提示词 (Prompt) 中，从而影响AI的当前认知、行为和回复内容。
  - 递归激活机制会按设定处理链式触发。 

4. 实时交互系统 @Yifeng Li 
定义: 用户与AI角色之间进行即时信息交换的各项功能，包括多种输入输出媒介及交互控制。
- 4.1 AI 自身状态管理与反馈 (AI Status Management & Feedback):
  - 4.1.1 状态定义:
    - Offline (离线)
    - Loading (加载中)
    - Idle (空闲)
    - Waiting (等待输入中)
    - Listening (聆听中)
    - Thinking (思考中)
    - Speaking (说话中)
    - Interrupted (被打断)
  - 4.1.2 状态展示: 系统应能追踪并（可选地通过UI 或者虚拟形象的动画变化）向用户展示AI的当前主要状态。
- 4.2 用户输入方式 (User Input Methods):
  - 4.2.1 文本输入:
    - 用户可以通过设备键盘在输入框中输入文本。
    - 支持通过“发送”按钮或“回车键”提交文本。
  - 4.2.2 语音输入:
    - 4.2.2.1 语音活动检测 (VAD):
      - 前端实现 VAD (Voice Activity Detection) 来智能判断语音输入的开始和结束，减少误触发和过早截断。 
      - VAD应支持用户可配置或自适应调整的关键参数： 
        - Speech Probability Threshold (语音概率阈值)
        - Silence Duration Threshold / Negative Speech Threshold (静音持续阈值)
        - Redemption Frames (语音结束缓冲帧数)
    - 4.2.2.2 关键词唤醒 (Wake up word):
      - 支持关键词唤醒 (Wake up word / Hotword detection) 功能，用户说出特定词汇后自动打开麦克风。
    - 4.2.2.3 ASR 辅助信息: (Future) ASR（自动语音识别）识别到的辅助信息（如情感、语速、环境噪音分类）允许选择性地加入到 Prompt 的上下文中。
- 4.3 用户打断AI机制 (User Interruption Mechanism):
  - 4.3.1 语音打断: 当AI处于Thinking或Speaking状态时，用户可以通过开始说话（触发VAD）来打断AI。 
    - 前端应有机制判断是否为有效的打断意图（例如，持续一定时长的语音输入），而非瞬间的杂音（misfire prevention），确认后再向后端发送打断信号。
  - 4.3.2 按钮打断: 用户可以通过界面上明确的“打断”按钮来中断AI的当前Thinking或Speaking过程。
  - 4.3.3 打断后的处理 (同步问题):
    - 打断信号发送至后端后，后端应立即停止当前的 ASR / Generation / TTS 等任务。
    - 后端应能记录（通过前端每句话的 ACK 获知）AI响应在被打断前已经播放到了哪一部分。
- 4.4 视觉信息输入 (Visual Information Input):
  - 4.4.1 图片/短视频上传:
    - 用户可以从本地设备选择并上传图片或短视频文件给AI。
    - 用户可以直接将剪贴板中的图片粘贴到输入框进行发送。
    - AI应能接收这些媒体文件，并理解其内容。
  - 4.4.2 视频链接分享:
    - 用户可以发送主流视频网站（如 Bilibili, YouTube）的视频链接。
    - AI可（通过 8. 工具集成）调用工具获取视频的标题、摘要、字幕等信息进行理解。
  - 4.4.3 摄像头画面共享:
    - 用户可以选择开启设备摄像头，将画面分享给AI（需用户明确授权）。
  - 4.4.4 屏幕/窗口内容共享:
    - 用户可以选择共享特定浏览器窗口或整个屏幕的画面给AI（需用户明确授权）。
    - 在桌宠模式下，（需用户授权）AI可配置为通过工具调用 (MCP) 主动请求或定时截取用户当前活动窗口的屏幕截图，以了解用户正在进行的操作。
- 4.5 麦克风精细控制 (Microphone Fine-grained Control):
  - 4.5.1 用户可以手动开启或关闭麦克风的语音输入功能。
  - 4.5.2 可配置选项：一段时间无语音活动后，麦克风自动关闭（进入待命或需手动重开状态）。
  - 4.5.3 可配置选项（自动切换逻辑）： 
    - 当AI开始Speaking时，自动关闭/静音用户麦克风（防止AI自己的声音被识别为用户输入）。
    - 当AI被用户成功打断后，自动开启/取消静音用户麦克风，进入Listening状态。
- 4.6 (Future) 上下文积累缓冲 (Context Accumulation Buffer):
  - 4.6.1 系统可以持续、低强度地收集短时间内的多模态信息，即使这些信息未立即触发AI的响应（例如，未达到VAD激活阈值的微弱声音片段，或摄像头/屏幕内容的低帧率采样）。
  - 4.6.2 这些积累的“潜意识”信息可以在AI下一次被正式激活时，作为更丰富的背景上下文输入，帮助AI理解更细微的环境变化或用户未明确表达的意图。
- 4.7 系统输出 (System Output):
  - 4.7.1 TTS语音输出: 用户能清晰、流畅地听到AI通过TTS生成的语音。 
    - 支持音量调节。
    - 支持用户选择（或自定义，如可能）不同的AI音色
    - 如可能，支持用户微调TTS参数（语速、音调）。
  - 4.7.2 字幕同步显示: 用户能看到与TTS语音输出实时同步的句子级别（或词级别，如可能）字幕。 
    - 字幕应准确对应当前正在播放的语音内容。
    - (Future) 允许用户自定义字幕样式（大小、颜色、背景）。
- 4.8 AI对话行为特性 (AI Conversational Characteristics):
  - (Future) 4.8.1 策略性沉默 (Strategic Silence): AI应能根据对话上下文、用户情绪、信息密度等多因素判断，在适当时机保持沉默或仅做简单应答（如点头的动画、嗯、哦），而非总是滔滔不绝。
  - 4.8.2 回应信息量控制 (Response Informativeness Control): AI生成的回应长度和信息密度应可动态调整或具备自适应能力，避免信息过载或过于简略，力求自然。
  - 4.8.3 对话风格控制 (Conversational Style Control):
    - AI 输出内容应采用自然的口语化表达，避免使用难以朗读的特殊符号和书面化语言，确保 TTS 能流畅播放。
  - 4.8.4 对话时Prompt构成 (Information Aggregation for Prompt Construction): AI的响应生成依赖于动态构建的Prompt，其主要构成要素包括： 
    - 4.8.4.1 角色相关信息 (Character Information): 来自 1. 角色 的相关信息。
    - 4.8.4.2 用户相关信息 (User Information): 来自 2. 用户的相关信息
    - 4.8.4.3 短期历史 (Short-term History):
      - 允许积累 Token 到一个阈值，自动进行对话总结
      - 允许设置图片是否计入并等效为 Token 成本
    - 4.8.4.4 世界系统注入 (WorldInfo Insertion): 来自 3. 世界系统 的、当前被激活的相关词条内容。
    - 4.8.4.5 角色故事和记忆检索 (Character Story and Memory Retrieval): 来自 6. 角色故事和记忆系统 的与当前情境相关的记忆片段。
    - 4.8.4.6 MCP 注入 (MCP Insertion): 通过 工具集成和外部信息获取 检索到的信息，需要 AI 主动提供检索关键词来调用工具检索获取，如 Web Search。
  - 4.8.5 世界信息集成 (WorldInfo Integration): 确保世界系统（模块3）提供的信息能够有效融入到Prompt中，影响AI的行为和对话内容。


5. 虚拟形象表现 @Yifeng Li 
定义: AI角色的视觉呈现方式及其与AI状态、语音、用户交互的联动表现。
- 5.1 形象类型支持 (Supported Avatar Types):
  - (Future) 5.1.1 静态图片 (Static Images):
    - 支持使用单张或多张静态图片作为角色形象。
    - 允许用户为同一角色配置多张图片， 可根据 AI 输出的 Tag 切换图片。
  - 5.1.2 Live2D 模型:
    - ******* 模型加载与显示:
      - 系统需能在画布内正确加载和渲染标准的Live2D模型文件 (Cubism3 ~ Cubism5)。
    - ******* 模型设置
      - 用户可以拖动模型
      - 用户可以调节模型大小
      - 用户可以设置是否开启鼠标跟随和触摸交互。
      - 用户可以具体配置模型的指针交互 (Pointer Interaction)，参考 1.角色
    - ******* 口型同步 (Lip Sync):
      - 基于AI输出的TTS音频流，实时驱动Live2D模型的嘴部动作。
        - 方式一 (ParamMouthOpenY直接控制)
        - (Future) 方式二 (Motionsync 支持)
    - ******* 状态对应动画与表现 (State-Specific Animations & Expressions):
      - Thinking (思考中) 状态表现: 
        - 如果模型中定义了特定的“Thinking”待机动画组 (.motion3.json 文件)，则在AI处于Thinking状态时按配置的概率分布播放这些动画。
      - Speaking (说话中) 状态表现: 
        - 如果模型中定义了适用于“Speaking”的待机动画组，则在TTS播放语音时同步按配置的概率分布播放这些动画（注意：此类动画不应与口型动画冲突）。类似Neuro-sama说话时身体的轻微晃动。
      - Idle (空闲) 状态表现: 
        - 随机播放模型预设的默认待机动画 (Idle Motion)。
      - (Future) Listening (聆听中) 状态表现。
      - (Future) Interrupted (被打断) 状态表现。
    - 5.1.2.5 AI指令控制表情与动作 (Tag-Controlled Expressions & Motions):
      - LLM在生成回复文本时，可以内嵌特定格式的控制标签（Tags），用于触发Live2D的预设表情和动作。
    - 5.1.2.6 指针交互 (Pointer Interaction):
      - 模型可以跟随用户鼠标，可以在设置中单独关闭。
      - Live2D模型可以定义多个触摸区域 (Hit Areas，如头部、身体)。
      - 用户通过鼠标点击或触摸屏触摸这些区域时，可以触发模型按配置的概率分布播放预设的一个反馈动作。
      - (Future) 触摸事件本身可以作为一种输入信号传递给AI，AI可以根据触摸的部位和时机做出相应的对话回应。
  - 5.1.3 (Future) 3D 模型

6. 角色故事和记忆系统 @Tim Chiu 

- 6.1 角色 Storybook (角色自身信息)  
- 6.2 记忆系统  
  - 6.2.1 记忆系统的作用范围  
    - 6.2.1.1 一个角色只有一个记忆  
    - 6.2.1.2 用户可以开启隐私模式，让这次Conversation / 这个 World 内容不写入长期记忆。  
    - 6.2.1.3 世界中的所有过去对话，都能被回忆  
  - 6.2.2 记忆系统应该实现的效果  
    - AI 需要在对话中没有相关信息的状况下，仍能知道某些信息。  
    - 低 retrieval 延迟  
    - 6.2.2.1 非被动记忆 (一直存在于 context 中，不需要对话中提及相关内容，AI 可以主动提出)  
      - 6.2.2.1.1 梗 & 印象深刻的东西
        - 6.2.2.1.1.1 角色能一直记得过去交流中的梗  
        - 6.2.2.1.1.2 能学习对话中创造的玩笑和共同回忆  
      - 6.2.2.1.2 角色对用户和其他角色 (如果有多角色) 存在自己的理解
        - AI 能理解自己于其他角色 (目前只有用户，之后多用户时会有其他角色) 的关系
          - 用户画像？ 
        - AI 能记录当下和最近，自己对其他角色的情绪，并随着时间变化更新  
      - 6.2.2.1.3 近期对话的理解
        - 6.2.2.1.3.1 AI 能大概知道最近对话中的事以及近期对话的内容（短期时间线），不需要被动提及就能主动由 AI 说出。
      - 6.2.2.1.4 AI 笔记
        - AI 能主动选择记住某些信息。
      - 6.2.2.1.5 对角色的人格的补足，修正与进化
        - 
    - 6.2.2.2 被动记忆 (对话中或系统提示词中出现相关内容，AI "回忆"或"联想"出来的东西)  
      - 6.2.2.2.1 AI 能获得相关话题或实体的全局理解
        - 6.2.2.2.1.1 实体本身的信息，要涵盖所有过去相关对话中体现的理解
        - 6.2.2.2.1.2 实体信息在长时间对话中发生的变化
        - 6.2.2.2.1.3 实体与其他相关实体关系的理解
      - 6.2.2.2.2 根据当前聊天内容，提取出最近与当前对话相关的聊天记录  
      - 6.2.2.2.3 使用 QC 手段，让语义上不相似的东西也能被回忆出来，提升联想性能

7. 主动交互能力 @Yifeng Li @Tim Chiu @Haolin Zhang 
定义: AI角色在没有用户直接即时输入的情况下，根据内部逻辑或外部触发，主动向用户发起不同形式的交互.
- 7.1 触发机制 (Trigger Mechanisms):
  - 7.1.1 基于计时器的触发 (Timer-based Trigger):
    - 闲置触发: 当 AI 处于Idle状态超过一段可配置的时间长度。
      - 用户可以设置时间长度的范围，在该范围中，AI 处于闲置状态的时间越长，触发主动对话的概率越高（线性增加）
    - 周期性触发: 按设定的时间间隔（每天、每周），在固定的时间（例如，每天早上9点）触发。
    - 主动对话的上下文:
      - 当前短期上下文
      - 角色故事和记忆系统中的信息（如可聊的话题、用户之前表达过的兴趣点、未完成的约定、最近的共同经历等）
      - 允许AI在触发时，先通过 8. 工具集成 进行网络搜索（如查询天气、与用户兴趣相关的新闻、节日提醒等）来寻找合适的话题。
  - 7.1.2 基于事件的触发 (Event-based Trigger):
    - (Future) 基于定时任务:
      - AI 可以设置定时任务，在未来的某个时间点触发，比如：10 点提醒我开会。
    - (Future) 外部应用/服务事件:
      - 例如，连接到用户的日历，当某个日程即将开始时，AI主动提醒。
      - 连接到新闻源，当用户关注的领域有重大新闻时，AI主动分享。
      - 特定纪念日（如用户生日，520 节日、需用户预设或AI从对话中学习）到达时，AI主动问候。
    - (Future) 系统内部 (故事书) 事件:
      - 例如，AI的角色故事书中某个“剧情点”按预设时间线解锁，故事书可由官方维护更新。
    - (Future) 用户行为模式分析触发:
      - 例如，AI监测到用户长时间未进行某项习惯性操作，可能会进行提醒或询问。
  - 7.1.3 (Future) 上下文感知触发 (Context-Aware Trigger - 尤其适用于桌宠模式):
    - AI通过视觉输入（屏幕共享）或工具获取到用户当前正在做的事情（如正在看某部电影、玩某款游戏），并结合自身知识和记忆，发起相关的、有情境的对话。
    - 例如：“这部电影我也‘看’过设定资料，你觉得主角怎么样？”
- 7.2 主动交互的形式 (Forms of Proactive Interaction):
  - 7.2.1 发起聊天: (AI 处于 Idle 状态) 主动发送一条消息或开始一段语音对话。
  - 7.2.2 (Future) 发送操作系统级通知/提醒:
    - 当AI应用未处于前台或AI处于Offline状态时（例如，用户关闭了应用但AI的后台服务仍在计时），可以通过系统通知栏向用户发送简短提醒或问候（需用户授权通知权限）。
    - 例如：“主人，现在是下午茶时间，要不要休息一下？”
  - 7.2.3 (Future) 主动执行工具/任务:
    - 例如，根据预设或学习到的习惯，在特定时间主动为用户播放音乐、打开某个常用网页。
- 7.3 用户控制与偏好设置 (User Control & Preferences):
  - 7.3.1 总开关: 用户可以完全开启或关闭AI的主动交互功能。
  - 7.3.2 频率与时段控制:
    - 用户可以设置两次主动发起之间的最小间隔
    - 用户可以设置主动交互的频率上限（如：低、中、高，或具体的次数/天）。
    - 用户可以设定“免打扰”时间段，在此期间AI不会主动发起交互。
  - 7.3.3 (Future) 按触发类型控制: 用户可以针对不同类型的触发器（如计时器、事件）分别设置是否启用。
  - 7.3.4 (Future) 按交互形式控制: 用户可以选择允许哪些形式的主动交互（如允许主动发消息，但不允许发送系统通知）。

8. 工具集成和外部信息获取 @Yifeng Li 
定义: 系统赋予AI通过结构化的方式调用外部API、服务或内部功能模块（统称为“工具”）的能力，以扩展其知识获取、信息处理和与外部世界交互的能力。MCP 是一种调用不同工具的协议。
- 8.1 内置默认工具 (Built-in Default Tools):
  - 8.1.1 默认可以直接启用，无需用户额外配置:
    - 网络查找 (Web Search): 
      - AI可以调用搜索引擎API（如Google Search, Bing Search, Serper API等）根据关键词进行网络搜索。
      - 能够从搜索结果中提取摘要、链接等信息。
    - 时间与日期操作 (Time & Date Operations): 
      - 获取当前准确的日期、时间、星期。
      - 进行简单的日期时间计算（如“三天后是几号？”）。
    - (Future) 故事书和长期记忆查询: AI 可以主动用关键词在故事书和长期记忆系统里面查询，或者管理。
  - (Future) 8.1.2 默认支持，但需要用户明确授权和配置才能启用:
    - Gmail (和其他邮件服务): 
      - 授权后，AI可以：读取邮件标题和摘要、根据指令起草邮件、发送邮件。
    - 社交媒体 (如 X, Discord 等): 
      - 授权后，AI可以：读取用户动态、获取特定话题的公开信息、根据指令发布状态/消息。
    - 日历服务 (Google Calendar, Outlook Calendar): 查询日程、创建提醒。
    - 地图与位置服务: 查询地点信息、规划路线。
- (Future) 8.2 用户自定义/第三方工具配置 (User-Configurable / Third-Party Tools):
  - 8.2.1 系统应提供一个接口或界面，允许高级用户或开发者为AI添加新的自定义 MCP 工具。
  - 8.2.3 工具管理: 用户可以查看已集成的工具列表、启用/禁用特定工具、编辑配置、删除工具。
  - 8.2.4  工具市场/插件系统: 提供一个平台，开发者可以发布工具，用户可以浏览和一键安装。
- (Future) 8.3 权限、安全与错误处理 (Permissions, Security & Error Handling):
  - 8.4.1 权限管理: 在执行敏感操作前获得用户授权，或者获得用户的细粒度授权。用户应能随时查看和撤销已授予的权限。
  - 8.4.2 数据安全: 工具调用过程中传输的数据（尤其是敏感信息）需要加密；存储的API密钥等凭证需要安全加密存储。
  - 8.4.3 调用限制: 对工具的调用频率、数据量等进行限制，防止滥用或意外的高额费用。
  - 8.4.4 错误处理: 工具调用可能会失败（网络问题、API错误、授权过期等）。系统需要妥善处理这些错误，并将错误信息以友好的方式告知AI（以便AI决定重试或告知用户），避免流程卡死。
  - 8.4.5 透明度: 用户应该能看到 AI 正在使用什么工具、做什么、工具处于的状态，以及其他相关的工具信息。

9. 世界、内部对话与消息记录
- 9.1 世界与内部对话 (Worlds and Internal Chats):
  - 9.1.1 系统应主要以 “世界 (World)” 为单位进行组织。一个“世界”可以包含一个或多个AI角色，并且在每个世界内部，用户可以开启一个或多个独立的“对话 (Chat)”实例。每个“对话”都是一条隔离的消息记录流。
  - 9.1.2 UI界面上:
    - 9.1.2.a 应有一个专门区域展示所有用户参与过的“世界”列表 (World List)。
    - 9.1.2.b 点击一个“世界”后，应展示该“世界”内部包含的“对话”列表 (Chat List within the selected World)。
  - 9.1.3 “世界”列表中的每个条目 (Each World entry in the World List) 至少应显示:
    - 世界名称 (World Name) (作为主要标识)。
    - 该世界内所有“对话”中，最新一条消息的相关信息（摘要、相对时间等）。
    - (Future) 参与该世界的主要AI角色的头像构成的集合。
  - 9.1.4 “对话”列表中的每个条目至少应显示:
    - 对话名称 (Chat Name) (如果用户未命名，可基于参与的AI角色名生成默认名称，或显示如 "与 [AI角色名] 的对话")。
    - 该“对话”内最新一条消息的相关信息（摘要、相对时间等）。
    - (Future) 参与该特定“对话”的AI角色的头像。
  - 9.1.5 用户可以通过点击“世界”列表中的条目进入该世界的“对话”列表视图，或按设计直接进入某个“对话”的消息记录视图。通过点击“对话”列表条目（如果存在）进入该“对话”的详细消息记录视图。
  - 9.1.6 支持对“世界”进行操作 (Operations on "Worlds"):
    - 删除世界: 用户可以删除整个“世界”，这将同时删除其内部所有的“对话”及其消息记录。此操作会影响AI的记忆（具体影响范围需定义，例如是否清除与该世界相关的所有记忆）。
    - (Future) 置顶世界: 将重要的“世界”固定在“世界”列表顶部。
    - 搜索世界: 根据世界名称、参与的AI角色名等关键词搜索“世界”。
    - (Future) 归档世界: 用户可以选择归档不再活跃的“世界”，其所有“对话”和消息记录将被归档处理。
  - 9.1.7 持对“对话”进行操作 (Operations on "Chats" within a World):
    - 删除“对话”内的聊天记录: 用户可以清空某个特定“对话”内的所有聊天记录，但这不应影响AI角色在该“世界”中的核心记忆或长期记忆（除非设计如此）。
    - (Future) 重命名“对话”。
    - (Future) 置顶“对话”（在所属“世界”的“对话”列表内）。
    - (Future) 搜索“对话”（在当前“世界”内，根据“对话”名称、参与的AI角色名或消息内容关键词）。
    - (Future) 归档“对话”（在所属“世界”内）。
- 9.2 “对话”消息记录 (Chat Chat History View):
  - 9.2.1 进入特定“对话”后，按时间顺序展示该“对话”内用户与所有参与AI角色之间的所有交互消息。
  - 9.2.2 每条消息应清晰展示：
    - 发送方:
      - 用户的人格名称 ({{user}}) /用户头像。
      - AI角色的名称 ({{char}}) /AI角色头像。
    - 消息内容:
      - 语音消息（可点击播放，显示时长，并附带ASR转换的文本）。
      - 图片消息（可点击放大预览）。
      - 文本消息。
      - (Future) 文件、视频片段等其他媒体类型。
    - 消息发送时间戳。
  - 9.2.3 消息加载: 支持滚动加载该“对话”更早的历史消息（分页加载或无限滚动）。
  - 9.2.4 消息操作:
    - (Future) 删除单条消息:
      - 用户可以删除自己发送的消息。此操作不会影响当前 AI 的记忆，只是隐藏消息。
      - 用户可以撤回自己发送的消息。此操作会影响当前 AI 的短期记忆，但不会影响 AI 的长期记忆 (如果撤回的消息已经在长期记忆中更新)
    - (Future) 引用回复特定消息。
    - (Future) 收藏消息。
  - 9.2.5 (Future) “对话”内消息搜索: 在当前打开的“对话”的消息记录中搜索关键词。
---
