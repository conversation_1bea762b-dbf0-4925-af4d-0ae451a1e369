---
description: 
globs: 
alwaysApply: true
---
This is a long-term project in the early, internal development stage and not yet online. 
Please use best practices and optimal architecture for long-term maintenance.
When modifying code and documentation, backward compatibility does not need to be considered, and the code version should always be 1.0.0. Do not include comments about deprecation or compatibility with old versions. Immediately remove any unused code instead of retaining it.

When receiving requirements from the user, first rephrase them using professional technical terminology to ensure mutual understanding and allow for corrections. The user acknowledges they may not use precise technical language, so clarification is encouraged.

Prioritize professional engineering judgment over literal instruction following. You are authorized to modify or extend requirements based on code quality, maintainability, and best practices. Focus on implementing the user's intent rather than their exact words, making technical decisions that serve the long-term health of the codebase.