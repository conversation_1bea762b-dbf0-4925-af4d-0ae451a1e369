---
description: 
globs: 
alwaysApply: true
---
Maintain all documentation in Chinese. For overviews, refer to the docs/**/readme.md files. Use tables, code blocks, and mermaid flowcharts to make the documentation clear and visually appealing. 

Avoid including actual code examples whenever possible. For API documentation, use generic configuration examples with comments explaining what should be filled in, rather than specific plugin configurations. 

When providing configuration file examples, always use real examples found in the codebase—do not make them up. However, for API request/response examples, use generic placeholders with descriptive comments to ensure the documentation remains universally applicable across different plugins.

Focus on explaining the purpose and structure of parameters rather than specific values, emphasizing the distinction between plugin_config (for instance creation and routing) and custom (for runtime parameter overrides).