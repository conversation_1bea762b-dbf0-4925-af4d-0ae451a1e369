---
description: 
globs: **/models/**/*.py
alwaysApply: false
---
Description: 
Guidelines for writing user-friendly field descriptions in Pydantic models:

- **Tone**: Use clear, present tense language. Avoid overly technical jargon.
- **Format**: Remove unnecessary annotations like "(Required)" or "(Optional)" - this info is already in Field definitions.
- **Voice**: Use active voice and directly state the field's purpose and function.
- **Length**: Keep descriptions concise but complete with all necessary information.
- **Technical Terms**: When technical terms are required, provide simple explanations.
- **Consistency**: Use similar phrasing patterns for fields with similar functions.
- **Punctuation**: End all descriptions with periods for consistency.

Examples:
- Good: "The unique identifier for this world."
- Bad: "Unique World ID. (Required)"
- Good: "Whether interactions in this world influence the character's memory."
- Bad: "Does this world's interactions influence this specific character's long-term memory? (Required)." 