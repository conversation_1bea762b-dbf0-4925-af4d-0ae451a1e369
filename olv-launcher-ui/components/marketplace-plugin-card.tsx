import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Download,
  Trash2,
  ArrowUpCircle,
  ExternalLink,
  Star,
  Calendar,
  User,
  Package,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { mutate } from 'swr';

import { MarketplacePluginInfo } from '@/lib/api';
import { useMarketplaceActions } from '@/hooks/use-marketplace';
import { useInstallationProgress } from '@/hooks/use-marketplace';

interface MarketplacePluginCardProps {
  plugin: MarketplacePluginInfo;
  onRefresh?: () => void;
}

export function MarketplacePluginCard({ plugin, onRefresh }: MarketplacePluginCardProps) {
  const { installPlugin, uninstallPlugin, updatePlugin, refreshPluginStatuses, loading } = useMarketplaceActions();
  const { data: installProgress } = useInstallationProgress(
    loading[`install_${plugin.name}`] || loading[`update_${plugin.name}`] ? plugin.name : null
  );

  const [showDetails, setShowDetails] = useState(false);

  const isInstalling = loading[`install_${plugin.name}`] || installProgress?.status === 'installing';
  const isUpdating = loading[`update_${plugin.name}`] || installProgress?.status === 'updating';
  const isUninstalling = loading[`uninstall_${plugin.name}`];
  const isLoading = isInstalling || isUpdating || isUninstalling;

  const handleInstall = async () => {
    try {
      await installPlugin(plugin.name);
      // Wait a moment for the installation to complete, then refresh statuses
      setTimeout(async () => {
        await refreshPluginStatuses();
        onRefresh?.();
        // Also refresh the main plugins list
        mutate('plugins');
      }, 1000);
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleUninstall = async () => {
    try {
      await uninstallPlugin(plugin.name);
      // Wait a moment for the uninstallation to complete, then refresh statuses
      setTimeout(async () => {
        await refreshPluginStatuses();
        onRefresh?.();
        // Also refresh the main plugins list
        mutate('plugins');
      }, 1000);
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleUpdate = async () => {
    try {
      await updatePlugin(plugin.name);
      // Wait a moment for the update to complete, then refresh statuses
      setTimeout(async () => {
        await refreshPluginStatuses();
        onRefresh?.();
        // Also refresh the main plugins list
        mutate('plugins');
      }, 1000);
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const getStatusBadge = () => {
    switch (plugin.status) {
      case 'available':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Available</Badge>;
      case 'installed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Installed</Badge>;
      case 'update_available':
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Update Available</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getServiceTypeBadge = () => {
    const colors = {
      asr: 'bg-purple-100 text-purple-800 border-purple-200',
      tts: 'bg-green-100 text-green-800 border-green-200',
      llm: 'bg-blue-100 text-blue-800 border-blue-200',
    };

    return (
      <Badge className={colors[plugin.service_type as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
        {plugin.service_type.toUpperCase()}
      </Badge>
    );
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Card className="transition-all duration-200 hover:shadow-md flex flex-col h-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <CardTitle className="text-lg font-semibold">{plugin.name}</CardTitle>
            <CardDescription className="text-sm text-muted-foreground line-clamp-2">
              {plugin.description || 'No description available'}
            </CardDescription>
          </div>
          <div className="flex flex-col gap-2 items-end ml-4">
            {getServiceTypeBadge()}
            {getStatusBadge()}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 space-y-4">
        {/* Plugin Info */}
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <Package size={14} className="text-muted-foreground" />
            <span className="font-medium">v{plugin.version}</span>
            {plugin.installed_version && plugin.installed_version !== plugin.version && (
              <span className="text-muted-foreground">(installed: v{plugin.installed_version})</span>
            )}
          </div>

          {plugin.author && (
            <div className="flex items-center gap-2">
              <User size={14} className="text-muted-foreground" />
              <span>{plugin.author}</span>
            </div>
          )}

          {plugin.rating && (
            <div className="flex items-center gap-2">
              <Star size={14} className="text-muted-foreground" />
              <span>{plugin.rating.toFixed(1)}/5.0</span>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Download size={14} className="text-muted-foreground" />
            <span>{plugin.download_count} downloads</span>
          </div>

          {plugin.updated_at && (
            <div className="flex items-center gap-2">
              <Calendar size={14} className="text-muted-foreground" />
              <span>Updated {formatDate(plugin.updated_at)}</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {plugin.tags && plugin.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {plugin.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {plugin.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs">
                +{plugin.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}

        {/* Installation Progress */}
        {installProgress && (isInstalling || isUpdating) && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Loader2 size={14} className="animate-spin" />
              <span>{installProgress.current_step}</span>
            </div>
            <Progress value={installProgress.progress_percentage} className="h-2" />
            <div className="text-xs text-muted-foreground">
              Step {installProgress.current_step_index} of {installProgress.total_steps}
              ({installProgress.progress_percentage.toFixed(0)}%)
            </div>
          </div>
        )}

        {/* Error Message */}
        {installProgress?.error_message && (
          <div className="flex items-center gap-2 text-sm text-red-600">
            <AlertCircle size={14} />
            <span>{installProgress.error_message}</span>
          </div>
        )}

        {/* Additional Details */}
        {showDetails && (
          <div className="space-y-2 text-sm border-t pt-3">
            <div>
              <span className="font-medium">Size:</span> {formatFileSize(plugin.file_size)}
            </div>
            {plugin.license && (
              <div>
                <span className="font-medium">License:</span> {plugin.license}
              </div>
            )}
            {plugin.dependencies && plugin.dependencies.length > 0 && (
              <div>
                <span className="font-medium">Dependencies:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {plugin.dependencies.map((dep) => (
                    <Badge key={dep} variant="outline" className="text-xs">
                      {dep}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-3 border-t">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="text-xs"
            >
              {showDetails ? 'Less' : 'More'} Info
            </Button>

            {plugin.repository_url && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(plugin.repository_url, '_blank')}
                className="text-xs flex items-center gap-1"
              >
                <ExternalLink size={12} />
                Repo
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            {plugin.status === 'available' && (
              <Button
                size="sm"
                onClick={handleInstall}
                disabled={isLoading}
                className="flex items-center gap-1"
              >
                {isInstalling ? (
                  <Loader2 size={14} className="animate-spin" />
                ) : (
                  <Download size={14} />
                )}
                Install
              </Button>
            )}

            {plugin.status === 'installed' && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleUninstall}
                disabled={isLoading}
                className="flex items-center gap-1"
              >
                {isUninstalling ? (
                  <Loader2 size={14} className="animate-spin" />
                ) : (
                  <Trash2 size={14} />
                )}
                Uninstall
              </Button>
            )}

            {plugin.status === 'update_available' && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleUpdate}
                  disabled={isLoading}
                  className="flex items-center gap-1"
                >
                  {isUpdating ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : (
                    <ArrowUpCircle size={14} />
                  )}
                  Update
                </Button>

                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleUninstall}
                  disabled={isLoading}
                  className="flex items-center gap-1"
                >
                  {isUninstalling ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : (
                    <Trash2 size={14} />
                  )}
                  Uninstall
                </Button>
              </>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
