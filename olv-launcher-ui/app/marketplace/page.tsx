'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  RefreshCw,
  Search,
  Download,
  Package,
  AlertCircle,
  Store,
  Filter,
  SortAsc,
  SortDesc,
  CheckCircle,
  Clock,
  ArrowUpCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { mutate } from 'swr';

import { useMarketplacePlugins, useMarketplaceActions, useMarketplaceSearch, usePluginStatus } from '@/hooks/use-marketplace';
import { MarketplacePluginCard } from '@/components/marketplace-plugin-card';
import { Navigation } from '@/components/navigation';
import { MarketplacePluginInfo } from '@/lib/api';

export default function MarketplacePage() {
  const { searchParams, updateSearch, resetSearch } = useMarketplaceSearch();
  const { data: marketplaceData, error: marketplaceError, isLoading: marketplaceLoading, mutate: refreshData } = useMarketplacePlugins(searchParams);
  const { refreshMarketplace, checkUpdates, loading } = useMarketplaceActions();

  const [activeTab, setActiveTab] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');

  const plugins = marketplaceData?.plugins || [];
  const { getStatusCounts, getPluginsByStatus, getUpdatablePlugins } = usePluginStatus(plugins);
  const statusCounts = getStatusCounts();

  // Handle search input
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateSearch({ query: searchQuery });
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, updateSearch]);

  const handleRefreshMarketplace = async () => {
    try {
      await refreshMarketplace(true);
      await refreshData();
      // Also refresh the main plugins list
      mutate('plugins');
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleCheckAllUpdates = async () => {
    try {
      await checkUpdates();
      await refreshData();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleServiceTypeFilter = (serviceType: string) => {
    updateSearch({ service_type: serviceType === 'all' ? '' : serviceType });
  };

  const handleSortChange = (sortBy: string) => {
    const currentOrder = searchParams.sort_order || 'asc';
    const newOrder = searchParams.sort_by === sortBy && currentOrder === 'asc' ? 'desc' : 'asc';
    updateSearch({ sort_by: sortBy, sort_order: newOrder });
  };

  const getFilteredPlugins = (status?: string) => {
    if (!status || status === 'all') return plugins;
    return getPluginsByStatus(status);
  };

  const renderPluginGrid = (pluginList: MarketplacePluginInfo[]) => {
    if (pluginList.length === 0) {
      return (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Package size={48} className="text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Plugins Found</h3>
            <p className="text-muted-foreground text-center max-w-md">
              {searchParams.query ?
                'No plugins match your search criteria. Try adjusting your filters.' :
                'No plugins are available in this category.'
              }
            </p>
          </CardContent>
        </Card>
      );
    }

    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {pluginList.map((plugin) => (
          <MarketplacePluginCard
            key={plugin.name}
            plugin={plugin}
            onRefresh={refreshData}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <Navigation />

      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Store size={32} />
            Plugin Marketplace
          </h1>
          <p className="text-muted-foreground">
            Discover and install plugins for your OLV platform
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleCheckAllUpdates}
            disabled={loading.checkUpdates}
            className="flex items-center gap-2"
          >
            {loading.checkUpdates ? (
              <RefreshCw size={16} className="animate-spin" />
            ) : (
              <ArrowUpCircle size={16} />
            )}
            Check Updates
          </Button>

          <Button
            variant="outline"
            onClick={handleRefreshMarketplace}
            disabled={loading.refresh}
            className="flex items-center gap-2"
          >
            {loading.refresh ? (
              <RefreshCw size={16} className="animate-spin" />
            ) : (
              <RefreshCw size={16} />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {marketplaceError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load marketplace: {marketplaceError.message}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={handleRefreshMarketplace}
              disabled={loading.refresh}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter size={20} />
            Search & Filter
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
                <Input
                  placeholder="Search plugins..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={searchParams.service_type || 'all'} onValueChange={handleServiceTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Service Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="asr">ASR</SelectItem>
                <SelectItem value="tts">TTS</SelectItem>
                <SelectItem value="llm">LLM</SelectItem>
              </SelectContent>
            </Select>

            <Select value={searchParams.sort_by || 'name'} onValueChange={handleSortChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort By" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="rating">Rating</SelectItem>
                <SelectItem value="downloads">Downloads</SelectItem>
                <SelectItem value="updated">Updated</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => {
                const newOrder = searchParams.sort_order === 'asc' ? 'desc' : 'asc';
                updateSearch({ sort_order: newOrder });
              }}
              className="flex items-center gap-2"
            >
              {searchParams.sort_order === 'desc' ? <SortDesc size={16} /> : <SortAsc size={16} />}
            </Button>

            <Button variant="outline" onClick={resetSearch}>
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Status Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <Package size={16} />
            All ({plugins.length})
          </TabsTrigger>
          <TabsTrigger value="available" className="flex items-center gap-2">
            <Download size={16} />
            Available ({statusCounts.available})
          </TabsTrigger>
          <TabsTrigger value="installed" className="flex items-center gap-2">
            <CheckCircle size={16} />
            Installed ({statusCounts.installed})
          </TabsTrigger>
          <TabsTrigger value="update_available" className="flex items-center gap-2">
            <ArrowUpCircle size={16} />
            Updates ({statusCounts.updateAvailable})
          </TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <TabsContent value="all">
            {marketplaceLoading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="animate-spin" size={24} />
                <span className="ml-2">Loading marketplace...</span>
              </div>
            ) : (
              renderPluginGrid(getFilteredPlugins('all'))
            )}
          </TabsContent>

          <TabsContent value="available">
            {renderPluginGrid(getFilteredPlugins('available'))}
          </TabsContent>

          <TabsContent value="installed">
            {renderPluginGrid(getFilteredPlugins('installed'))}
          </TabsContent>

          <TabsContent value="update_available">
            {renderPluginGrid(getFilteredPlugins('update_available'))}
          </TabsContent>
        </div>
      </Tabs>

      {/* Stats */}
      {marketplaceData && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>
                Showing {plugins.length} of {marketplaceData.total_count} plugins
              </span>
              {marketplaceData.has_more && (
                <span>More results available - refine your search to see all</span>
              )}
            </div>
          </CardContent>
        </Card>
      )}
      </div>
    </div>
  );
}
