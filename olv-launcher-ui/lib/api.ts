import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7000';

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 600000, // 10 minutes
  headers: {
    'Content-Type': 'application/json',
  },
});

// Status types matching backend enums
export enum PluginStatus {
  RUNNING = 'running',
  STOPPED = 'stopped',
  ERROR = 'error',
  STARTING = 'starting',
  STOPPING = 'stopping',
}

// Types for API responses
export interface PluginInfo {
  name: string;
  service_type: string;
  status: PluginStatus;
  service_url?: string;
  is_local: boolean;
  description?: string;
  version?: string;
  author?: string;
}

export interface PluginStartResponse {
  message: string;
  plugin_name: string;
  service_url: string;
  status: PluginStatus;
}

export interface PluginStopResponse {
  message: string;
  plugin_name: string;
  status: PluginStatus;
}

export interface PluginHealthResponse {
  plugin_name: string;
  status: PluginStatus;
  service_url?: string;
  http_status?: number;
  error?: string;
  message?: string;
}

export interface SchemaResponse {
  plugin_name: string;
  json_schema: Record<string, unknown>;
  ui_schema: Record<string, unknown>;
}

export interface PluginSchemasResponse {
  plugin_name: string;
  schemas: {
    plugin_json_schema?: Record<string, unknown>;
    plugin_ui_schema?: Record<string, unknown>;
  };
}

// Instance management types
export interface PluginInstanceInfo {
  instance_id: string;
  ready: boolean;
}

export interface PluginInstanceCreateRequest {
  config: Record<string, unknown>;
}

export interface PluginInstanceCreateResponse {
  message: string;
  plugin_name: string;
  instance_id: string;
  config: Record<string, unknown>;
}

export interface PluginInstanceListResponse {
  plugin_name: string;
  instances: {
    total_instances: number;
    instances: Record<string, PluginInstanceInfo>;
  };
}

export interface PluginInstanceDeleteResponse {
  message: string;
  plugin_name: string;
  instance_id: string;
}

// Workspace management types
export interface WorkspacePluginState {
  name: string;
  should_be_running: boolean;
  instances: Array<{
    instance_id: string;
    config: Record<string, unknown>;
  }>;
}

export interface WorkspaceState {
  name: string;
  created_at: string;
  description?: string;
  plugins: WorkspacePluginState[];
}

export interface WorkspaceConfig {
  workspaces: Record<string, WorkspaceState>;
  current_workspace?: string;
  default_workspace?: string;
  auto_update_enabled: boolean;
}

export interface WorkspaceSaveRequest {
  name?: string;
  description?: string;
  save_to_current?: boolean;
}

export interface WorkspaceSaveResponse {
  message: string;
  workspace_name: string;
  saved_plugins: number;
  saved_instances: number;
}

export interface WorkspaceRestoreResponse {
  message: string;
  workspace_name: string;
  restored_plugins: number;
  restored_instances: number;
  errors?: string[];
}

export interface WorkspaceListResponse {
  workspaces: WorkspaceState[];
  current_workspace?: string;
}

export interface WorkspaceDeleteResponse {
  message: string;
  workspace_name: string;
}

export interface WorkspaceSettingsRequest {
  default_workspace?: string;
  auto_update_enabled?: boolean;
}

export interface WorkspaceSettingsResponse {
  message: string;
  default_workspace?: string;
  auto_update_enabled: boolean;
}

// Marketplace types
export interface MarketplacePluginInfo {
  name: string;
  service_type: string;
  version: string;
  description: string;
  author: string;
  repository_url: string;
  download_url: string;
  file_size?: number;
  dependencies: string[];
  tags: string[];
  license: string;
  created_at?: string;
  updated_at?: string;
  download_count: number;
  rating?: number;
  status: string; // available, installed, update_available
  installed_version?: string;
}

export interface MarketplaceSearchRequest {
  query?: string;
  service_type?: string;
  tags?: string[];
  author?: string;
  min_rating?: number;
  sort_by?: string;
  sort_order?: string;
  limit?: number;
  offset?: number;
}

export interface MarketplacePluginListResponse {
  plugins: MarketplacePluginInfo[];
  total_count: number;
  has_more: boolean;
}

export interface PluginInstallRequest {
  plugin_name: string;
}

export interface PluginInstallResponse {
  message: string;
  plugin_name: string;
  status: string;
  progress_id: string;
}

export interface PluginInstallProgressResponse {
  plugin_name: string;
  status: string;
  progress_percentage: number;
  current_step: string;
  total_steps: number;
  current_step_index: number;
  error_message?: string;
  started_at: string;
  completed_at?: string;
}

export interface PluginUninstallRequest {
  plugin_name: string;
}

export interface PluginUninstallResponse {
  message: string;
  plugin_name: string;
}

export interface PluginUpdateCheckResponse {
  plugin_name: string;
  current_version: string;
  latest_version: string;
  update_available: boolean;
  changelog: string;
}

export interface PluginUpdateRequest {
  plugin_name: string;
}

export interface PluginUpdateResponse {
  message: string;
  plugin_name: string;
  old_version: string;
  new_version: string;
  progress_id: string;
}

export interface MarketplaceRefreshResponse {
  message: string;
  plugin_count: number;
  last_updated: string;
}

// API functions
export const apiClient = {

  // Plugin management
  async getAllPlugins(): Promise<{ plugins: PluginInfo[] }> {
    const response = await api.get('/plugins/');
    return response.data;
  },

  async getPluginsByService(serviceType: string): Promise<{ service_type: string; plugins: PluginInfo[] }> {
    const response = await api.get(`/plugins/${serviceType}`);
    return response.data;
  },

  async startPlugin(pluginName: string): Promise<PluginStartResponse> {
    const response = await api.post(`/plugins/${pluginName}/start`);
    return response.data;
  },

  async stopPlugin(pluginName: string): Promise<PluginStopResponse> {
    const response = await api.post(`/plugins/${pluginName}/stop`);
    return response.data;
  },

  async getPluginHealth(pluginName: string): Promise<PluginHealthResponse> {
    const response = await api.get(`/plugins/${pluginName}/health`);
    return response.data;
  },

  // Schema management
  async getPluginSchemas(pluginName: string): Promise<PluginSchemasResponse> {
    const response = await api.get(`/plugins/${pluginName}/schemas`);
    return response.data;
  },

  async getPluginSchemasDetailed(pluginName: string): Promise<SchemaResponse> {
    const response = await api.get(`/plugins/${pluginName}/schemas/plugin`);
    return response.data;
  },

  // Instance management
  async createPluginInstance(pluginName: string, config: Record<string, unknown>): Promise<PluginInstanceCreateResponse> {
    const response = await api.post(`/plugins/${pluginName}/instances`, { config });
    return response.data;
  },

  async listPluginInstances(pluginName: string): Promise<PluginInstanceListResponse> {
    const response = await api.get(`/plugins/${pluginName}/instances`);
    return response.data;
  },

  async deletePluginInstance(pluginName: string, instanceId: string): Promise<PluginInstanceDeleteResponse> {
    const response = await api.delete(`/plugins/${pluginName}/instances/${instanceId}`);
    return response.data;
  },

  // Logs
  async getPluginLogs(pluginName: string, lines: number = 100): Promise<{ plugin_name: string; logs: string[]; total_lines: number; has_more: boolean }> {
    const response = await api.get(`/plugins/${pluginName}/logs?lines=${lines}`);
    return response.data;
  },

  async clearPluginLogs(pluginName: string): Promise<{ message: string; plugin_name: string }> {
    const response = await api.delete(`/plugins/${pluginName}/logs`);
    return response.data;
  },

  // Workspace management
  async saveWorkspace(request: WorkspaceSaveRequest): Promise<WorkspaceSaveResponse> {
    const response = await api.post('/workspaces/save', request);
    return response.data;
  },

  async restoreWorkspace(workspaceName: string): Promise<WorkspaceRestoreResponse> {
    const response = await api.post(`/workspaces/${workspaceName}/restore`);
    return response.data;
  },

  async listWorkspaces(): Promise<WorkspaceListResponse> {
    const response = await api.get('/workspaces');
    return response.data;
  },

  async deleteWorkspace(workspaceName: string): Promise<WorkspaceDeleteResponse> {
    const response = await api.delete(`/workspaces/${workspaceName}`);
    return response.data;
  },

  async getWorkspaceConfig(): Promise<WorkspaceConfig> {
    const response = await api.get('/workspaces/config');
    return response.data;
  },

  async updateWorkspaceSettings(request: WorkspaceSettingsRequest): Promise<WorkspaceSettingsResponse> {
    const response = await api.post('/workspaces/settings', request);
    return response.data;
  },

  async getWorkspaceSettings(): Promise<WorkspaceSettingsResponse> {
    const response = await api.get('/workspaces/settings');
    return response.data;
  },

  // Marketplace management
  async getMarketplacePlugins(searchParams?: MarketplaceSearchRequest): Promise<MarketplacePluginListResponse> {
    const params = new URLSearchParams();
    if (searchParams?.query) params.append('query', searchParams.query);
    if (searchParams?.service_type) params.append('service_type', searchParams.service_type);
    if (searchParams?.author) params.append('author', searchParams.author);
    if (searchParams?.min_rating) params.append('min_rating', searchParams.min_rating.toString());
    if (searchParams?.sort_by) params.append('sort_by', searchParams.sort_by);
    if (searchParams?.sort_order) params.append('sort_order', searchParams.sort_order);
    if (searchParams?.limit) params.append('limit', searchParams.limit.toString());
    if (searchParams?.offset) params.append('offset', searchParams.offset.toString());
    if (searchParams?.tags) {
      searchParams.tags.forEach(tag => params.append('tags', tag));
    }

    const response = await api.get(`/marketplace/plugins?${params.toString()}`);
    return response.data;
  },

  async refreshMarketplace(force: boolean = false): Promise<MarketplaceRefreshResponse> {
    const response = await api.post(`/marketplace/refresh?force=${force}`);
    return response.data;
  },

  async installPlugin(pluginName: string): Promise<PluginInstallResponse> {
    const response = await api.post('/marketplace/install', { plugin_name: pluginName });
    return response.data;
  },

  async getInstallationProgress(pluginName: string): Promise<PluginInstallProgressResponse> {
    const response = await api.get(`/marketplace/install/${pluginName}/progress`);
    return response.data;
  },

  async uninstallPlugin(pluginName: string): Promise<PluginUninstallResponse> {
    const response = await api.post('/marketplace/uninstall', { plugin_name: pluginName });
    return response.data;
  },

  async checkUpdates(pluginNames?: string[]): Promise<PluginUpdateCheckResponse[]> {
    const params = new URLSearchParams();
    if (pluginNames) {
      pluginNames.forEach(name => params.append('plugin_names', name));
    }
    const response = await api.get(`/marketplace/updates?${params.toString()}`);
    return response.data;
  },

  async updatePlugin(pluginName: string): Promise<PluginUpdateResponse> {
    const response = await api.post('/marketplace/update', { plugin_name: pluginName });
    return response.data;
  },

  async clearInstallationProgress(pluginName: string): Promise<{ message: string }> {
    const response = await api.delete(`/marketplace/install/${pluginName}/progress`);
    return response.data;
  },

  async refreshPluginStatuses(): Promise<{ message: string }> {
    const response = await api.post('/marketplace/refresh-statuses');
    return response.data;
  },
};