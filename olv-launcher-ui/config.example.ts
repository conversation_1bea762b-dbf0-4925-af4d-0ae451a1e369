// Configuration for OLV Launcher UI
// Copy this file to config.ts and modify as needed

export const config = {
  // Backend API URL - change this to your OLV Launcher backend URL
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7000',
  
  // Refresh intervals (in milliseconds)
  healthRefreshInterval: 5000,    // 5 seconds
  pluginsRefreshInterval: 3000,   // 3 seconds
  logsRefreshInterval: 3000,      // 3 seconds
  
  // UI Configuration
  theme: {
    defaultTheme: 'light',
    enableDarkMode: true,
  },
  
  // Feature flags
  features: {
    autoRefresh: true,
    realTimeLogs: true,
    pluginConfiguration: true,
  },
}; 