import { useState, useCallback } from 'react';
import useS<PERSON> from 'swr';
import { apiClient, PluginInfo, PluginHealthResponse } from '@/lib/api';
import { toast } from 'sonner';

export const usePlugins = () => {
  return useSWR<{ plugins: PluginInfo[] }>('plugins', apiClient.getAllPlugins, {
    refreshInterval: 3000, // Refresh every 3 seconds
    onError: (error) => {
      console.error('Failed to fetch plugins:', error);
    },
  });
};

export const usePluginsByService = (serviceType: string) => {
  return useSWR<{ service_type: string; plugins: PluginInfo[] }>(
    serviceType ? `plugins/${serviceType}` : null,
    () => apiClient.getPluginsByService(serviceType),
    {
      refreshInterval: 3000,
      onError: (error) => {
        console.error(`Failed to fetch ${serviceType} plugins:`, error);
      },
    }
  );
};

export const usePluginHealth = (pluginName: string | null) => {
  return useSWR<PluginHealthResponse | null>(
    pluginName ? `plugin/${pluginName}/health` : null,
    pluginName ? () => apiClient.getPluginHealth(pluginName) : null,
    {
      refreshInterval: 5000,
      onError: (error) => {
        console.error(`Failed to check health for ${pluginName}:`, error);
      },
    }
  );
};

export const usePluginActions = () => {
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const startPlugin = useCallback(async (pluginName: string) => {
    setLoading(prev => ({ ...prev, [pluginName]: true }));
    try {
      const result = await apiClient.startPlugin(pluginName);
      toast.success(`Plugin ${pluginName} started successfully at ${result.service_url}`);
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to start ${pluginName}: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, [pluginName]: false }));
    }
  }, []);

  const stopPlugin = useCallback(async (pluginName: string) => {
    setLoading(prev => ({ ...prev, [pluginName]: true }));
    try {
      const result = await apiClient.stopPlugin(pluginName);
      toast.success(`Plugin ${pluginName} stopped successfully`);
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to stop ${pluginName}: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, [pluginName]: false }));
    }
  }, []);

  const clearLogs = useCallback(async (pluginName: string) => {
    try {
      await apiClient.clearPluginLogs(pluginName);
      toast.success(`Logs cleared for ${pluginName}`);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to clear logs for ${pluginName}: ${errorMessage}`);
      throw error;
    }
  }, []);

  return {
    startPlugin,
    stopPlugin,
    clearLogs,
    loading,
  };
};