import { useState, useCallback } from 'react';
import useS<PERSON> from 'swr';
import {
  apiClient,
  MarketplacePluginInfo,
  MarketplaceSearchRequest,
  MarketplacePluginListResponse,
  PluginInstallProgressResponse,
  PluginUpdateCheckResponse,
  MarketplaceRefreshResponse
} from '@/lib/api';
import { toast } from 'sonner';

export const useMarketplacePlugins = (searchParams?: MarketplaceSearchRequest) => {
  const cacheKey = searchParams ? `marketplace/plugins?${JSON.stringify(searchParams)}` : 'marketplace/plugins';

  return useSWR<MarketplacePluginListResponse>(
    cacheKey,
    () => apiClient.getMarketplacePlugins(searchParams),
    {
      refreshInterval: 0, // Don't auto-refresh marketplace data
      onError: (error) => {
        console.error('Failed to fetch marketplace plugins:', error);
      },
    }
  );
};

export const useInstallationProgress = (pluginName: string | null) => {
  return useSWR<PluginInstallProgressResponse | null>(
    pluginName ? `marketplace/install/${pluginName}/progress` : null,
    pluginName ? () => apiClient.getInstallationProgress(pluginName) : null,
    {
      refreshInterval: 2000, // Refresh every 2 seconds during installation
      onError: (error) => {
        // Don't show error toast for 404 (no progress found)
        if (error?.response?.status !== 404) {
          console.error(`Failed to get installation progress for ${pluginName}:`, error);
        }
      },
    }
  );
};

export const useUpdateCheck = (pluginNames?: string[]) => {
  const cacheKey = pluginNames ? `marketplace/updates?${JSON.stringify(pluginNames)}` : 'marketplace/updates';

  return useSWR<PluginUpdateCheckResponse[]>(
    cacheKey,
    () => apiClient.checkUpdates(pluginNames),
    {
      refreshInterval: 0, // Don't auto-refresh update checks
      onError: (error) => {
        console.error('Failed to check for updates:', error);
      },
    }
  );
};

export const useMarketplaceActions = () => {
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const refreshMarketplace = useCallback(async (force: boolean = false): Promise<MarketplaceRefreshResponse> => {
    setLoading(prev => ({ ...prev, refresh: true }));
    try {
      const result = await apiClient.refreshMarketplace(force);
      toast.success(`Marketplace refreshed! Found ${result.plugin_count} plugins.`);
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to refresh marketplace: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, refresh: false }));
    }
  }, []);

  const installPlugin = useCallback(async (pluginName: string) => {
    setLoading(prev => ({ ...prev, [`install_${pluginName}`]: true }));
    try {
      const result = await apiClient.installPlugin(pluginName);
      toast.success(`Installation started for ${pluginName}`);
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to start installation for ${pluginName}: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, [`install_${pluginName}`]: false }));
    }
  }, []);

  const uninstallPlugin = useCallback(async (pluginName: string) => {
    setLoading(prev => ({ ...prev, [`uninstall_${pluginName}`]: true }));
    try {
      const result = await apiClient.uninstallPlugin(pluginName);
      toast.success(`${pluginName} uninstalled successfully`);
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to uninstall ${pluginName}: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, [`uninstall_${pluginName}`]: false }));
    }
  }, []);

  const updatePlugin = useCallback(async (pluginName: string) => {
    setLoading(prev => ({ ...prev, [`update_${pluginName}`]: true }));
    try {
      const result = await apiClient.updatePlugin(pluginName);
      toast.success(`Update started for ${pluginName}`);
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to start update for ${pluginName}: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, [`update_${pluginName}`]: false }));
    }
  }, []);

  const checkUpdates = useCallback(async (pluginNames?: string[]) => {
    setLoading(prev => ({ ...prev, checkUpdates: true }));
    try {
      const result = await apiClient.checkUpdates(pluginNames);
      const updatesAvailable = result.filter(update => update.update_available).length;
      if (updatesAvailable > 0) {
        toast.success(`Found ${updatesAvailable} plugin updates available`);
      } else {
        toast.success('All plugins are up to date');
      }
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to check for updates: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, checkUpdates: false }));
    }
  }, []);

  const clearInstallationProgress = useCallback(async (pluginName: string) => {
    try {
      await apiClient.clearInstallationProgress(pluginName);
    } catch (error: unknown) {
      console.error(`Failed to clear installation progress for ${pluginName}:`, error);
    }
  }, []);

  const refreshPluginStatuses = useCallback(async () => {
    setLoading(prev => ({ ...prev, refreshStatuses: true }));
    try {
      await apiClient.refreshPluginStatuses();
      toast.success('Plugin statuses refreshed successfully');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to refresh plugin statuses: ${errorMessage}`);
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, refreshStatuses: false }));
    }
  }, []);

  return {
    refreshMarketplace,
    installPlugin,
    uninstallPlugin,
    updatePlugin,
    checkUpdates,
    clearInstallationProgress,
    refreshPluginStatuses,
    loading,
  };
};

// Helper hook for managing marketplace search state
export const useMarketplaceSearch = () => {
  const [searchParams, setSearchParams] = useState<MarketplaceSearchRequest>({
    query: '',
    service_type: '',
    sort_by: 'name',
    sort_order: 'asc',
    limit: 50,
    offset: 0,
  });

  const updateSearch = useCallback((updates: Partial<MarketplaceSearchRequest>) => {
    setSearchParams(prev => ({
      ...prev,
      ...updates,
      offset: updates.offset !== undefined ? updates.offset : 0, // Reset offset when other params change
    }));
  }, []);

  const resetSearch = useCallback(() => {
    setSearchParams({
      query: '',
      service_type: '',
      sort_by: 'name',
      sort_order: 'asc',
      limit: 50,
      offset: 0,
    });
  }, []);

  return {
    searchParams,
    updateSearch,
    resetSearch,
  };
};

// Helper hook for plugin status management
export const usePluginStatus = (plugins: MarketplacePluginInfo[]) => {
  const getStatusCounts = useCallback(() => {
    const counts = {
      available: 0,
      installed: 0,
      updateAvailable: 0,
    };

    plugins.forEach(plugin => {
      switch (plugin.status) {
        case 'available':
          counts.available++;
          break;
        case 'installed':
          counts.installed++;
          break;
        case 'update_available':
          counts.updateAvailable++;
          break;
      }
    });

    return counts;
  }, [plugins]);

  const getPluginsByStatus = useCallback((status: string) => {
    return plugins.filter(plugin => plugin.status === status);
  }, [plugins]);

  const getUpdatablePlugins = useCallback(() => {
    return plugins.filter(plugin => plugin.status === 'update_available');
  }, [plugins]);

  return {
    getStatusCounts,
    getPluginsByStatus,
    getUpdatablePlugins,
  };
};
