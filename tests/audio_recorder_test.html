<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Recorder - Base64 Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .recording {
            background: #dc3545 !important;
        }
        .recording:hover {
            background: #c82333 !important;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
            color: #666;
        }
        .audio-player {
            text-align: center;
            margin: 20px 0;
        }
        .output {
            margin: 20px 0;
        }
        .output label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .output textarea {
            width: 100%;
            height: 150px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            resize: vertical;
        }
        .copy-btn {
            background: #28a745;
            margin-top: 10px;
        }
        .copy-btn:hover {
            background: #218838;
        }
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .api-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        .api-example pre {
            background: #343a40;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Audio Recorder - Base64 Generator</h1>
        
        <div class="info">
            <strong>Instructions:</strong>
            <ul>
                <li>Click "Start Recording" button to begin recording</li>
                <li>Click "Stop Recording" when finished</li>
                <li>The system will automatically generate Base64 encoded audio data</li>
                <li>Audio format: 16-bit PCM, 16kHz (suitable for ASR recognition)</li>
            </ul>
        </div>

        <div class="controls">
            <button id="startBtn">🎤 Start Recording</button>
            <button id="stopBtn" disabled>⏹️ Stop Recording</button>
            <button id="clearBtn">🗑️ Clear</button>
        </div>

        <div class="status" id="status">Ready to record...</div>

        <div class="audio-player" id="audioPlayer" style="display: none;">
            <audio controls id="audioElement"></audio>
        </div>

        <div class="output">
            <label for="base64Output">Base64 Audio Data:</label>
            <textarea id="base64Output" placeholder="Base64 encoding will appear here after recording..."></textarea>
            <button class="copy-btn" id="copyBtn" onclick="copyToClipboard()">📋 Copy Base64</button>
        </div>

        <div class="api-example">
            <label><strong>API Request Example:</strong></label>
            <pre id="apiExample">
{
  "audio_data": "Actual Base64 data will appear here after recording",
  "character_specific_config": {
    "use_itn": true,
    "whisper_language": "zh",
    "whisper_task": "transcribe",
    "character_name": "Test Character",
    "voice_style": "formal",
    "language_preference": "chinese"
  }
}
            </pre>
            <button class="copy-btn" onclick="copyApiExample()">📋 Copy API Example</button>
        </div>
    </div>

    <script>
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;

        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const clearBtn = document.getElementById('clearBtn');
        const status = document.getElementById('status');
        const audioPlayer = document.getElementById('audioPlayer');
        const audioElement = document.getElementById('audioElement');
        const base64Output = document.getElementById('base64Output');
        const apiExample = document.getElementById('apiExample');

        // Check browser support
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            status.textContent = '❌ Your browser does not support audio recording';
            startBtn.disabled = true;
        }

        startBtn.addEventListener('click', startRecording);
        stopBtn.addEventListener('click', stopRecording);
        clearBtn.addEventListener('click', clearAll);

        async function startRecording() {
            try {
                // Request microphone permission, set to 16kHz mono
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });

                // Create MediaRecorder
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });

                audioChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    await processAudio(audioBlob);
                    
                    // Stop all audio tracks
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.start(100); // Collect data every 100ms
                isRecording = true;

                // Update UI
                startBtn.disabled = true;
                stopBtn.disabled = false;
                startBtn.classList.add('recording');
                status.textContent = '🔴 Recording...';

            } catch (error) {
                console.error('Failed to start recording:', error);
                status.textContent = '❌ Failed to start recording: ' + error.message;
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;

                // Update UI
                startBtn.disabled = false;
                stopBtn.disabled = true;
                startBtn.classList.remove('recording');
                status.textContent = '⏳ Processing audio...';
            }
        }

        async function processAudio(audioBlob) {
            try {
                // Show audio player
                const audioUrl = URL.createObjectURL(audioBlob);
                audioElement.src = audioUrl;
                audioPlayer.style.display = 'block';

                // Convert to WAV format and generate Base64
                const wavBlob = await convertToWav(audioBlob);
                const base64Data = await blobToBase64(wavBlob);
                
                // Remove data URL prefix, keep only base64 data
                const base64Audio = base64Data.split(',')[1];
                
                base64Output.value = base64Audio;
                
                // Update API example
                updateApiExample(base64Audio);
                
                status.textContent = '✅ Recording complete! Base64 data generated';

            } catch (error) {
                console.error('Audio processing failed:', error);
                status.textContent = '❌ Audio processing failed: ' + error.message;
            }
        }

        async function convertToWav(audioBlob) {
            // Create AudioContext
            const audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000
            });
            
            // Convert blob to ArrayBuffer
            const arrayBuffer = await audioBlob.arrayBuffer();
            
            // Decode audio data
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            
            // Convert to 16kHz mono
            const length = audioBuffer.length;
            const sampleRate = 16000;
            const numberOfChannels = 1;
            
            // Create new AudioBuffer
            const offlineContext = new OfflineAudioContext(numberOfChannels, length, sampleRate);
            const source = offlineContext.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(offlineContext.destination);
            source.start();
            
            const renderedBuffer = await offlineContext.startRendering();
            
            // Convert to WAV format
            const wavArrayBuffer = audioBufferToWav(renderedBuffer);
            return new Blob([wavArrayBuffer], { type: 'audio/wav' });
        }

        function audioBufferToWav(buffer) {
            const length = buffer.length;
            const sampleRate = buffer.sampleRate;
            const numberOfChannels = buffer.numberOfChannels;
            
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            
            // WAV file header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, numberOfChannels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * numberOfChannels * 2, true);
            view.setUint16(32, numberOfChannels * 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);
            
            // Audio data
            const channelData = buffer.getChannelData(0);
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, channelData[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
            
            return arrayBuffer;
        }

        function blobToBase64(blob) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        }

        function updateApiExample(base64Data) {
            const example = {
                "audio_data": base64Data.substring(0, 50) + "...(complete base64 data)",
                "character_specific_config": {
                    "use_itn": true,
                    "whisper_language": "zh",
                    "whisper_task": "transcribe",
                    "character_name": "Test Character",
                    "voice_style": "formal",
                    "language_preference": "chinese"
                }
            };
            
            apiExample.textContent = JSON.stringify(example, null, 2);
        }

        function copyToClipboard() {
            base64Output.select();
            document.execCommand('copy');
            
            const originalText = document.querySelector('#copyBtn').textContent;
            document.querySelector('#copyBtn').textContent = '✅ Copied!';
            setTimeout(() => {
                document.querySelector('#copyBtn').textContent = originalText;
            }, 2000);
        }

        function copyApiExample() {
            const fullExample = {
                "audio_data": base64Output.value,
                "character_specific_config": {
                    "use_itn": true,
                    "whisper_language": "zh",
                    "whisper_task": "transcribe",
                    "character_name": "Test Character",
                    "voice_style": "formal",
                    "language_preference": "chinese"
                }
            };
            
            navigator.clipboard.writeText(JSON.stringify(fullExample, null, 2)).then(() => {
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '✅ Full example copied!';
                setTimeout(() => {
                    btn.textContent = originalText;
                }, 2000);
            });
        }

        function clearAll() {
            base64Output.value = '';
            audioPlayer.style.display = 'none';
            status.textContent = 'Ready to record...';
            apiExample.textContent = `
{
  "audio_data": "Actual Base64 data will appear here after recording",
  "character_specific_config": {
    "use_itn": true,
    "whisper_language": "zh",
    "whisper_task": "transcribe",
    "character_name": "Test Character",
    "voice_style": "formal",
    "language_preference": "chinese"
  }
}`;
        }
    </script>
</body>
</html> 