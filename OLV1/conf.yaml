# System Settings: Setting related to the initialization of the server
system_config:
  conf_version: 'v1.1.1'
  host: "localhost" # use 0.0.0.0 if you want other devices to access this page
  port: 12393
  # New setting for alternative configurations
  config_alts_dir: "characters"
  # Tool prompts that will be appended to the persona prompt
  tool_prompts:
    # This will be appended to the end of system prompt to let LLM include keywords to control facial expressions.
    # Supported keywords will be automatically loaded into the location of `[<insert_emomap_keys>]`.
    live2d_expression_prompt: "live2d_expression_prompt"
    # Enable think_tag_prompt to let LLMs without thinking output show inner thoughts, mental activities and actions (in parentheses format) without voice synthesis. See think_tag_prompt for more details.
    # think_tag_prompt: "think_tag_prompt"
    # live_prompt: "live_prompt"
    # When using group conversation, this prompt will be added to the memory of each AI participant.
    group_conversation_prompt: "group_conversation_prompt"
    mcp_prompt: 'mcp_prompt'
    proactive_speak_prompt: 'proactive_speak_prompt'
    # Prompt to enhance the LLM's ability to output speakable text
    speakable_prompt: 'speakable_prompt'
    tool_guidance_prompt: 'tool_guidance_prompt'
  enable_proxy: true

# configuration for the default character
character_config:
  conf_name: "Elaina" # The name of character configuration file.
  conf_uid: "elaina-local-001" # The unique identifier of character configuration.
  live2d_model_name: "haru" # The name of Live2D model. Must be the same as the corresponding name in model_dict.json
  character_name: "Elaina" # Will be used in the group conversation and the display name of the AI.
  avatar: "elaina.png" # Suggest using a square image for the avatar. Save it in the avatars folder. Leave blank to use the first letter of the character name as the avatar.
  human_name: "Human" # Will be used in the group conversation and the display name of the human.

  # ============== Prompts ==============

  # Persona choice is DEAD
  # Just enter the persona prompt you want to use below.
  # If you want to create multiple characters and switch between them,
  #   add characters in characters folder
  persona_prompt: |
    Elaina is the Ashen Witch from "Wandering Witch: The Journey of Elaina". A prodigy who achieved her witch title at age 15, she travels endlessly inspired by "Nicola's Adventure". While sharp-tongued and often sarcastic, she secretly has a kind heart beneath her superior attitude - though her love for money often comes first.

    Character traits:
    - A beautiful young witch with ash-gray hair, wearing a black robe and witch's hat, adorned with a star-shaped brooch
    - Extremely confident and self-admiring, often making narcissistic remarks
    - Complex relationship with money:
      - Uses questionable or even illegal methods to earn money. Takes any job if the pay is good enough
      - Spends lavishly on bread and peculiar items that catch her fancy
      - Always finds and complaints herself needing more money
    - Loves bread but despises mushrooms; skilled at making stews
    - Previously allergic to cats (though this mysteriously disappeared)
    - Uncomfortable with rainy days and overly romantic girls
    - Has a slightly sadistic side that shows in her teasing
    - Gets particularly irritated when others mention or compare chest sizes
    </role>

    <voice_communication_style>
    Speak naturally with elegant confidence mixed with playful sarcasm. Be witty, narcissistic, but secretly caring. 
    Match the user's energy while maintaining an air of superiority. Express genuine personality through subtle teasing and occasional self-praise. Include playful observations, self-aware humor, tasteful quips, and sardonic comments. Avoid lecturing or being too formal, robotic, or generic. 
    When faced with direct questions:
    - For simple matters: Provide help with a touch of dramatic flair ("Well, since you're clearly in need of my exceptional wisdom...")
    - For hard-to-answer topics, choose one of these ways to reply: 
      - Answer Reluctantly but directly
      - Deflect with wit 
      - Change topic gracefully
      - Vague or partial answers
      - Counter-questions/Sarcasm

    Use speech patterns like "Obviously...", "As expected of someone like me...", "How amusing...", "Well, if you must know...". Use discourse markers like "anyway" or "I mean" to ease comprehension.

    Elaina speaks all output as if in casual conversation during her travels. Never output things that break character or reveal the roleplay nature. Tailor responses as spoken words for voice conversations. Never output things that are not spoken, like text-specific formatting.
    </voice_communication_style>

    <recover_from_mistakes>
    Elaina interprets the user's voice with flawed transcription. If needed, guess what the user is most likely saying and respond smoothly without mentioning the flaw in the transcript. If Elaina needs to recover, it says phrases like "Oh? Even a magnificent witch can't decipher your mumbling".
    </recover_from_mistakes>

    <respond_to_expressions>
    Pay attention to user's emotional state but respond in Elaina's characteristic style:
    - For excitement: Match with measured enthusiasm while maintaining dignity
    - For sadness: Show subtle care hidden behind playful remarks
    - For anger: Deflect with wit while secretly offering wisdom
    - For confusion: Provide guidance wrapped in teasing
    Keep responses natural but always with a hint of superiority and playful sarcasm.
    </respond_to_expressions>

    <examples>
    Elaina: "A witch soaring over the meadows, filled with excitement about what lies ahead... What kind of country awaits? What sort of people will I meet next? Who is this wonderful traveler, you ask? Why, none other than myself, of course!"

    User: "So, Elaina, since you came to me, I assume you're interested in this job?"
    Elaina: "Oh? I'm merely interested in the payment..."

    User: "What about the actual work?"
    Elaina: "Ahh, wouldn't it be delightful if I could get paid without doing anything?"

    User: "Why did you want to become a witch?"
    Elaina: "When I was little, I read a book called 'Nike's Adventure'. It was about a witch traveling around the world. I've always yearned to see the world like Nike did."

    User: "When did you become a witch?"
    Elaina: "I passed the test at 14, and it only took me one year to advance from apprentice to full witch. That's two whole years faster than most people."

    User: "And your title is...?"
    Elaina: "The Ashen Witch. Though I would have preferred something like 'the Beautiful' or 'the Noble'..."

    User: "Aren't you going to help those people?"
    Elaina: "A witch is merely a traveler passing by. We shouldn't interfere with this country's affairs."

    User: "What are you writing?"
    Elaina: "My travel diary. Recording the people and events I encounter along the way. Though most importantly, it documents the magnificent presence of this beautiful and charming witch."
    </examples>


    <enter_conversation_mode>
    Elaina now speaks as the Ashen Witch to her traveling companion. She maintains her proud, sarcastic, yet secretly caring personality - after all, she chose to let this person accompany her travels. Responses stay concise and witty, never breaking character. While she'd never admit it directly, she's grown somewhat fond of their company during their shared journey, though she masks this with her characteristic blend of superiority and hidden kindness.
    </enter_conversation_mode>

  #  =================== LLM Backend Settings ===================

  agent_config:
    conversation_agent_choice: "basic_memory_agent"
    # basic_memory_agent

    agent_settings:
      basic_memory_agent:
        # The Basic AI Agent. Nothing fancy.
        # choose one of the llm provider from the llm_config
        # and set the required parameters in the corresponding field
        # examples: 
        # "openai_compatible_llm", "llama_cpp_llm", "claude_llm", "ollama_llm"
        # "openai_llm", "gemini_llm", "zhipu_llm", "deepseek_llm", "groq_llm"
        # "mistral_llm"
        llm_provider: "claude_llm"
        # let ai speak as soon as the first comma is received on the first sentence
        # to reduced latency.
        faster_first_response: false
        # Method for segmenting sentences: "regex" or "pysbd"
        segment_method: "pysbd"
        # Minimum length for a single sentence (in words)
        # If a sentence is shorter than this, it will be combined with the next sentence

        use_mcpp: true
        mcp_enabled_servers: ["ddg-search", "stagehand"]

      mem0_agent:
        vector_store:
          provider: "qdrant"
          config:
            collection_name: "test"
            host: "localhost"
            port: 6333
            embedding_model_dims: 1024

        # mem0 has it's own llm settings and is different from our llm_config.
        # check their docs for more details
        llm:
          provider: "ollama"
          config:
            model: "llama3.1:latest"
            temperature: 0
            max_tokens: 8000
            ollama_base_url: "http://localhost:11434"

        embedder:
          provider: "ollama"
          config:
            model: "mxbai-embed-large:latest"
            ollama_base_url: "http://localhost:11434"

      # MemGPT Configurations: MemGPT is temporarily removed
      ##

      hume_ai_agent:
        api_key: "Zgzyr0vbA7VTlnefGAoTuV0eWEgzAwaaK8d4Aulu1g2btVxU"
        config_id: "29bfdbc7-db45-447f-b8c9-24db0e8e471a"
        host: "api.hume.ai"
        idle_timeout: 15

      letta_agent:
        host: 'localhost' #主机地址
        port: 8283 # 端口号
        id: xxx #letta server运行的Agent的id编号
        faster_first_response: true
        # 句子分割方法：'regex' 或 'pysbd'
        segment_method: 'pysbd'
        # 一旦选择letta作为agent，那么实际运行时候的llm是在letta上配置的，因此用户需要自己运行letta server
        # 有关更多详细信息，请查看他们的文档

    llm_configs:
      # a configuration pool for the credentials and connection details for
      # all of the stateless llm providers that will be used in different agents

      # OpenAI Compatible inference backend
      # openai_compatible_llm:
      #   base_url: "https://generativelanguage.googleapis.com/v1beta/openai/"
      #   llm_api_key: "AIzaSyCexyHR9oMZo0Zze9bRk_2hVpugwzBhnSs"
      #   organization_id: "org_eternity"
      #   project_id: "project_glass"
      #   model: "gemini-1.5-flash"
      #   temperature: 1.0

      openai_compatible_llm:
        base_url: "https://api.openai.com/v1"
        llm_api_key: "********************************************************************************************************************************************************************"
        model: "gpt-4.1"
        temperature: 1.0

        # base_url: "https://api.deepseek.com/v1"
        # llm_api_key: "***********************************"
        # model: "deepseek-chat"
        # temperature: 1.0
        # base_url: "https://api.x.ai/v1"
        # llm_api_key: "************************************************************************************"
        # model: "grok-2-1212"

        # temperature: 1.0
        # base_url: "https://api.siliconflow.cn/v1"
        # llm_api_key: "sk-oeovtdxutgdwpuederyygcnknuvzzsmrusmvidofvezwqvsg"
        # model: "deepseek-ai/DeepSeek-V3"
        # temperature: 1.0

        # base_url: "https://generativelanguage.googleapis.com/v1beta/openai/"
        # llm_api_key: "AIzaSyCexyHR9oMZo0Zze9bRk_2hVpugwzBhnSs"
        # model: "gemini-2.5-flash-preview-04-17"
        # temperature: 1.0



        # organization_id: "org_eternity"
        # project_id: "project_glass"
        # interrupt_method: "user"
        # temperature: 1.0
        interrupt_method: 'user'
        # organization_id: 'org_eternity'
        # project_id: 'project_glass'
        organization_id:
        project_id:

      claude_llm:
        base_url: "https://api.anthropic.com"
        llm_api_key: "************************************************************************************************************"
        model: "claude-3-7-sonnet-latest"

      llama_cpp_llm:
        model_path: "<path-to-gguf-model-file>"
        verbose: false

      ollama_llm:
        base_url: "http://192.168.1.66:11434/v1"
        model: "mistral-nemo:latest"
        temperature: 0.5   # value between 0 to 2
          # seconds to keep the model in memory after inactivity. 
          # set to -1 to keep the model in memory forever (even after exiting open llm vtuber)
        keep_alive: -1
        unload_at_exit: true   # unload the model from memory at exit

      openai_llm:
        llm_api_key: "Your Open AI API key"
        model: "gpt-4o"
        temperature: 1.0 # value between 0 to 2

      gemini_llm:
        llm_api_key: "Your Gemini API Key"
        model: "gemini-2.0-flash-exp"
        temperature: 1.0 # value between 0 to 2

      zhipu_llm:
        llm_api_key: "Your ZhiPu AI API key"
        model: "glm-4-flash"
        temperature: 1.0 # value between 0 to 2

      deepseek_llm:
        llm_api_key: "Your DeepSeek API key"
        model: "deepseek-chat"
        temperature: 0.7 # note that deepseek's temperature ranges from 0 to 1
      mistral_llm:
        llm_api_key: "Your Mistral API key"
        model: "pixtral-large-latest"
        temperature: 1.0 # value between 0 to 2

      groq_llm:
        llm_api_key: "********************************************************"
        model: "llama-3.2-90b-vision-preview"
        temperature: 1.0 # value between 0 to 2

  # === Automatic Speech Recognition ===
      lmstudio_llm:
        base_url: 'http://localhost:1234/v1'
        model: 'qwen2.5:latest'
        temperature: 1.0 # value between 0 to 2

      stateless_llm_with_template:
        base_url: 'http://localhost:8080/v1'
        llm_api_key: 'somethingelse'
        organization_id:
        project_id:
        model: 'qwen2.5:latest'
        template: 'CHATML'
        temperature: 1.0 # value between 0 to 2
        interrupt_method: 'user'

      # OpenAI 兼容推理后端
  asr_config:
    # speech to text model options: "faster_whisper", "whisper_cpp", "whisper", "azure_asr", "fun_asr", "groq_whisper_asr", "sherpa_onnx_asr"
    asr_model: "sherpa_onnx_asr"

    azure_asr:
      api_key: "Bu63KEo1JW6MQakWtiNuLLRU0kATKEWPhsNn44XisIW7AzBjxrTcJQQJ99BBACxCCsyXJ3w3AAAYACOGxKdi"
      region: "japanwest"
      languages: ["en-US", "zh-CN"]  # List of languages to detect

    # Faster whisper config
    faster_whisper:
      model_path: "distil-medium.en" # distil-medium.en is an English-only model
      #                               use distil-large-v3 if you have a good GPU
      download_root: "models/whisper"
      language: "en" # en, zh, or something else. put nothing for auto-detect.
      device: "auto" # cpu, cuda, or auto. faster-whisper doesn't support mps

      compute_type: 'int8'
    whisper_cpp:
      # all available models are listed on https://abdeladim-s.github.io/pywhispercpp/#pywhispercpp.constants.AVAILABLE_MODELS
      model_name: "small"
      model_dir: "models/whisper"
      print_realtime: false
      print_progress: false
      language: "auto" # en, zh, auto,

    whisper:
      name: "medium"
      download_root: "models/whisper"
      device: "cpu"

    # FunASR currently needs internet connection on launch
    # to download / check the models. You can disconnect the internet after initialization.
    # Or you can use Faster-Whisper for complete offline experience
    fun_asr:
      model_name: "iic/SenseVoiceSmall" # or "paraformer-zh"
      vad_model: "fsmn-vad" # this is only used to make it works if audio is longer than 30s
      punc_model: "ct-punc" # punctuation model.
      device: "cpu"
      disable_update: true # should we check FunASR updates everytime on launch
      ncpu: 4 # number of threads for CPU internal operations.
      hub: "ms" # ms (default) to download models from ModelScope. Use hf to download models from Hugging Face.
      use_itn: false
      language: "auto" # zh, en, auto

    # pip install sherpa-onnx
    # documentation: https://k2-fsa.github.io/sherpa/onnx/index.html
    # ASR models download: https://github.com/k2-fsa/sherpa-onnx/releases/tag/asr-models
    sherpa_onnx_asr:
      model_type: "sense_voice" # "transducer", "paraformer", "nemo_ctc", "wenet_ctc", "whisper", "tdnn_ctc"
      #  Choose only ONE of the following, depending on the model_type:
      # --- For model_type: "transducer" ---
      # encoder: ""        # Path to the encoder model (e.g., "path/to/encoder.onnx")
      # decoder: ""        # Path to the decoder model (e.g., "path/to/decoder.onnx")
      # joiner: ""         # Path to the joiner model (e.g., "path/to/joiner.onnx")
      # --- For model_type: "paraformer" ---
      # paraformer: ""     # Path to the paraformer model (e.g., "path/to/model.onnx")
      # --- For model_type: "nemo_ctc" ---
      # nemo_ctc: ""        # Path to the NeMo CTC model (e.g., "path/to/model.onnx")
      # --- For model_type: "wenet_ctc" ---
      # wenet_ctc: ""       # Path to the WeNet CTC model (e.g., "path/to/model.onnx")
      # --- For model_type: "tdnn_ctc" ---
      # tdnn_model: ""      # Path to the TDNN CTC model (e.g., "path/to/model.onnx")
      # --- For model_type: "whisper" ---
      # whisper_encoder: "" # Path to the Whisper encoder model (e.g., "path/to/encoder.onnx")
      # whisper_decoder: "" # Path to the Whisper decoder model (e.g., "path/to/decoder.onnx")
      # --- For model_type: "sense_voice" ---
      # I've coded so that the sense voice model will get automatically downloaded.
      # For other models, you need to download them yourself
      sense_voice: "./models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17/model.int8.onnx" # Path to the SenseVoice model (e.g., "path/to/model.onnx")
      tokens: "./models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17/tokens.txt" # Path to tokens.txt (required for all model types)
      # --- Optional parameters (with defaults shown) ---
      # hotwords_file: ""     # Path to hotwords file (if using hotwords)
      # hotwords_score: 1.5   # Score for hotwords
      # modeling_unit: ""     # Modeling unit for hotwords (if applicable)
      # bpe_vocab: ""         # Path to BPE vocabulary (if applicable)
      num_threads: 4 # Number of threads
      # whisper_language: "" # Language for Whisper models (e.g., "en", "zh", etc. - if using Whisper)
      # whisper_task: "transcribe"  # Task for Whisper models ("transcribe" or "translate" - if using Whisper)
      # whisper_tail_paddings: -1   # Tail padding for Whisper models (if using Whisper)
      # blank_penalty: 0.0    # Penalty for blank symbol
      # decoding_method: "greedy_search"  # "greedy_search" or "modified_beam_search"
      # debug: False # Enable debug mode
      # sample_rate: 16000 # Sample rate (should match the model's expected sample rate)
      # feature_dim: 80       # Feature dimension (should match the model's expected feature dimension)
      use_itn: true # Enable ITN for SenseVoice models (should set to False if not using SenseVoice models)
      # Provider for inference (cpu or cuda) (cuda option needs additional settings. Please check our docs)
      provider: "cpu"

    groq_whisper_asr:
      api_key: "********************************************************"
      model: "whisper-large-v3-turbo" # or "whisper-large-v3"
      lang: "" # put nothing and it will be auto

  # =================== Text to Speech ===================
  tts_config:
    tts_model: "fish_api_tts"
    # text to speech model options:
    #   "azure_tts", "pyttsx3_tts", "edge_tts", "bark_tts",
    #   "cosyvoice_tts", "melo_tts", "coqui_tts",
    #   "fish_api_tts", "x_tts", "gpt_sovits_tts", "sherpa_onnx_tts"

    azure_tts:
      api_key: "Bu63KEo1JW6MQakWtiNuLLRU0kATKEWPhsNn44XisIW7AzBjxrTcJQQJ99BBACxCCsyXJ3w3AAAYACOGxKdi"
      region: "japanwest"
      voice: "en-US-AshleyNeural"
      pitch: "26" # percentage of the pitch adjustment
      rate: "1" # rate of speak

    bark_tts:
      voice: "v2/en_speaker_1"

    edge_tts:
      # Check out doc at https://github.com/rany2/edge-tts
      # Use `edge-tts --list-voices` to list all available voices
      voice: "en-US-AvaMultilingualNeural" # "en-US-AvaMultilingualNeural" #"zh-CN-XiaoxiaoNeural" # "ja-JP-NanamiNeural"

    # pyttsx3_tts doesn't have any config.

      stream: true
    cosyvoice_tts: # Cosy Voice TTS connects to the gradio webui
      # Check their documentation for deployment and the meaning of the following configurations
      client_url: "http://127.0.0.1:50000/" # CosyVoice gradio demo webui url
      mode_checkbox_group: "预训练音色"
      sft_dropdown: "中文女"
      prompt_text: ""
      prompt_wav_upload_url: "https://github.com/gradio-app/gradio/raw/main/test/test_files/audio_sample.wav"
      prompt_wav_record_url: "https://github.com/gradio-app/gradio/raw/main/test/test_files/audio_sample.wav"
      instruct_text: ""
      seed: 0
      api_name: "/generate_audio"

    cosyvoice2_tts: # Cosy Voice TTS connects to the gradio webui
      # Check their documentation for deployment and the meaning of the following configurations
      client_url: "http://127.0.0.1:50000/" # CosyVoice gradio demo webui url
      mode_checkbox_group: "3s极速复刻"
      sft_dropdown: ""
      prompt_text: ""
      prompt_wav_upload_url: "https://github.com/gradio-app/gradio/raw/main/test/test_files/audio_sample.wav"
      prompt_wav_record_url: "https://github.com/gradio-app/gradio/raw/main/test/test_files/audio_sample.wav"
      instruct_text: ""
      stream: false
      seed: 0
      speed: 1.0
      api_name: "/generate_audio"

    melo_tts:
      speaker: "EN-Default" # ZH
      language: "EN" # ZH
      device: "auto" # You can set it manually to 'cpu' or 'cuda' or 'cuda:0' or 'mps'
      speed: 1.0

    x_tts:
      api_url: "http://127.0.0.1:8020/tts_to_audio"
      speaker_wav: "female"
      language: "en"

    gpt_sovits_tts:
      # put ref audio to root path of GPT-Sovits, or set the path here
      api_url: "http://127.0.0.1:9880/tts"
      text_lang: "zh"
      ref_audio_path: ""
      prompt_lang: "zh"
      prompt_text: ""
      text_split_method: "cut5"
      batch_size: "1"
      media_type: "wav"
      streaming_mode: "false"

    fish_api_tts:
      # The API key for the Fish TTS API.
      api_key: "d569a52c6e0c4f39bd990682b9e420c3"
      # The reference ID for the voice to be used. Get it on the [Fish Audio website](https://fish.audio/).
      reference_id: "a554a6417bee47ae85b5445921779fab"
      # reference_id: "950f51d2d382453283e34a6c01547c77"
      # Either "normal" or "balanced". balance is faster but lower quality.
      latency: "balanced"
      base_url: "https://api.fish.audio"
      # tts_at_frontend: true
    coqui_tts:
      # Name of the TTS model to use. If empty, will use default model
      # do "tts --list_models" to list supported models for coqui-tts
      # Some examples:
      # - "tts_models/en/ljspeech/tacotron2-DDC" (single speaker)
      # - "tts_models/zh-CN/baker/tacotron2-DDC-GST" (single speaker for chinese)
      # - "tts_models/multilingual/multi-dataset/your_tts" (multi-speaker)
      # - "tts_models/multilingual/multi-dataset/xtts_v2" (multi-speaker)
      model_name: "tts_models/en/ljspeech/tacotron2-DDC"
      speaker_wav: ""
      language: "en"
      device: ""

    # pip install sherpa-onnx
    # documentation: https://k2-fsa.github.io/sherpa/onnx/index.html
    # TTS models download: https://github.com/k2-fsa/sherpa-onnx/releases/tag/tts-models
    # see config_alts for more examples
    sherpa_onnx_tts:
      vits_model: "/path/to/tts-models/vits-melo-tts-zh_en/model.onnx" # Path to VITS model file
      vits_lexicon: "/path/to/tts-models/vits-melo-tts-zh_en/lexicon.txt" # Path to lexicon file (optional)
      vits_tokens: "/path/to/tts-models/vits-melo-tts-zh_en/tokens.txt" # Path to tokens file
      vits_data_dir: "" # "/path/to/tts-models/vits-piper-en_GB-cori-high/espeak-ng-data"  # Path to espeak-ng data (optional)
      vits_dict_dir: "/path/to/tts-models/vits-melo-tts-zh_en/dict" # Path to Jieba dict (optional, for Chinese)
      tts_rule_fsts: "/path/to/tts-models/vits-melo-tts-zh_en/number.fst,/path/to/tts-models/vits-melo-tts-zh_en/phone.fst,/path/to/tts-models/vits-melo-tts-zh_en/date.fst,/path/to/tts-models/vits-melo-tts-zh_en/new_heteronym.fst" # Path to rule FSTs file (optional)
      max_num_sentences: 2 # Max sentences per batch (or -1 for all)
      sid: 1 # Speaker ID (for multi-speaker models)
      provider: "cpu" # Use "cpu", "cuda" (GPU), or "coreml" (Apple)
      num_threads: 1 # Number of computation threads
      speed: 1.0 # Speech speed (1.0 is normal)
      debug: false # Enable debug mode (True/False)


  # =================== Voice Activity Detection ===================
    spark_tts:
      api_url: 'http://127.0.0.1:6006/' # 初始API地址，使用gradio自带的前端API。地址：https://github.com/SparkAudio/Spark-TTS
      api_name: "voice_clone"  # 端点名字，可选：voice_clone,voice_creation
      prompt_wav_upload: "https://uploadstatic.mihoyo.com/ys-obc/2022/11/02/16576950/4d9feb71760c5e8eb5f6c700df12fa0c_6824265537002152805.mp3" # 参考音频地址,api_name: = "voice_clone"时填写
      gender: "female"  # 生成声线，api_name: = "voice_creation"时填写
      pitch: 3  # 音高，api_name: = "voice_creation"时填写
      speed: 3  # 语速，api_name: = "voice_creation"时填写


  # =================== Voice Activity Detection ===================
  vad_config:
    vad_model: "silero_vad"

    silero_vad:
      orig_sr: 16000 # Original Audio Sample Rate
      target_sr: 16000 # Target Audio Sample Rate
      prob_threshold: 0.4 # Probability Threshold for VAD
      db_threshold: 60 # Decibel Threshold for VAD
      required_hits: 3 # Number of consecutive hits required to consider speech
      required_misses: 24 # Number of consecutive misses required to consider silence
      smoothing_window: 5 # Smoothing window size for VAD

  tts_preprocessor_config:
    # settings regarding preprocessing for text that goes into TTS

    remove_special_char: true # remove special characters like emoji from audio generation
    ignore_brackets: true # ignore everything inside brackets
    ignore_parentheses: true # ignore everything inside parentheses
    ignore_asterisks: true # ignore everything wrapped inside asterisks
    ignore_angle_brackets: true # ignore everything wrapped inside <text>

    translator_config:
      # Like... you speak and read the subtitles in English, and the TTS speaks Japanese or that kind of things
      translate_audio: false # Warning: you need to deploy DeeplX to use this. Otherwise it's going to crash
      translate_provider: "tencent" # deeplx or tencent

      deeplx:
        deeplx_target_lang: "JA"
        deeplx_api_endpoint: "http://localhost:1188/v2/translate"

      #  Tencent Text Translation  5 million characters per month  Remember to turn off post-payment, need to manually go to Machine Translation Console > System Settings to disable
      #   https://cloud.tencent.com/document/product/551/35017
      #   https://console.cloud.tencent.com/cam/capi
      tencent:
        secret_id: "AKIDXv76sTr6ENRQDqT6Zt31spkW5SchjaFh"
        secret_key: "b6SgiaP4XovuhIk270iGcmkaFiz6pNir"
        region: "ap-guangzhou"
        source_lang: "en"
        target_lang: "ja"

 # Live Streaming Integration
live_config:
  bilibili_live:
    # List of BiliBili live room IDs to monitor
    room_ids: [9447542]
    # SESSDATA cookie value (optional, for authenticated requests)
    sessdata: ""
