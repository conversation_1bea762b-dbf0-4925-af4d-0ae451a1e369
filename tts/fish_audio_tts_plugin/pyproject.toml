[project]
name = "fish-audio-tts-plugin"
version = "1.0.0"
description = "Fish Audio TTS Plugin for OLV platform"
authors = [
    {name = "OLV Team"}
]
dependencies = [
   "fastapi>=0.115.12",
   "fish-audio-sdk>=2025.4.2",
   "httpx>=0.28.1",
   "loguru>=0.7.3",
   "ormsgpack>=1.10.0",
   "pydantic>=2.11.5",
   "pydantic-settings>=2.9.1",
   "uvicorn>=0.34.2",
   "websockets>=15.0.1",
]
requires-python = ">=3.10"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0"
]

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0"
]
