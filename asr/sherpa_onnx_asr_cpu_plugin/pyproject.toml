[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sherpa-onnx-asr-cpu-plugin"
version = "1.0.0"
description = "Sherpa-ONNX ASR Plugin for OLV"
authors = [
    {name = "OLV Team"}
]
dependencies = [
    "fastapi>=0.115.12",
    "loguru>=0.7.3",
    "numpy>=1.26.4",
    "onnxruntime>=1.22.0",
    "pydantic>=2.11.5",
    "pydantic-settings>=2.9.1",
    "python-multipart>=0.0.20",
    "requests>=2.32.3",
    "sherpa-onnx>=1.12.0",
    "tqdm>=4.67.1",
    "uvicorn>=0.34.2",
]
requires-python = ">=3.10"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
