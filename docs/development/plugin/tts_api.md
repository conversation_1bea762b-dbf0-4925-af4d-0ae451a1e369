# TTS Plugin API 结构

## 实例路由策略

### 最小超集匹配（Minimal Superset Matching）

TTS Plugin 使用**最小超集匹配策略**来选择最合适的引擎实例：

**匹配规则：**
1. **必要条件**：实例配置必须包含 `plugin_config` 中的所有字段，且值完全匹配
2. **允许额外字段**：实例配置可以包含 `plugin_config` 中没有的额外字段
3. **最优选择**：在所有匹配的实例中，选择配置字段数量最少的实例
4. **同等情况下**：如果有多个实例具有相同的最少字段数，选择第一个

## TTS Plugin 端点

TTS Plugin 提供以下端点：

### 1. POST `/synthesize`
合成语音数据，使用已存在的插件配置实例。

**请求体：**
```json
{
    "text": "要合成的文本内容",  // 待合成的文本
    "plugin_config": {
        // 插件特定的配置参数，用于标识和创建实例
        // 相同的 plugin_config 会复用同一个实例
        // 通常包含 API 密钥、模型选择、语音ID等需要长时间加载的配置
    },
    "custom": {
        // 可选的运行时参数，不参与实例路由
        // 用于覆盖默认设置，如语速、音量、延迟模式等
        // 这些参数不会影响实例的创建和复用
    }
}
```

**响应：**
```json
{
    "audio": "base64_encoded_audio_data",  // Base64 编码的音频数据
    "processing_time": 0.123,
    "instance_id": "abc123def456"  // 使用的实例ID（配置哈希）
}
```

**错误处理：**
- 如果 `plugin_config` 对应的实例不存在，返回 404 错误
- 不会自动创建新实例

### 2. WebSocket `/synthesize_stream`
流式语音合成，支持实时文本输入和音频输出。

**连接流程：**

1. **建立连接**
   ```
   ws://localhost:8000/synthesize_stream
   ```

2. **发送初始配置**
   ```json
   {
       "plugin_config": {
           // 插件特定的配置参数
       },
       "custom": {
           // 可选的运行时参数
       }
   }
   ```

3. **接收就绪信号**
   ```json
   {
       "status": "ready",
       "instance_id": "abc123def456"
   }
   ```

4. **发送文本块**
   ```json
   {
       "type": "text",
       "text": "文本片段 "
   }
   ```

5. **接收音频块**
   ```json
   {
       "type": "audio",
       "audio": "base64_encoded_audio_chunk"
   }
   ```

6. **发送结束信号**
   ```json
   {
       "type": "end"
   }
   ```

7. **接收完成信号**
   ```json
   {
       "type": "complete"
   }
   ```

**错误处理：**
- 如果配置无效，连接会被关闭并返回错误信息
- 如果引擎不支持流式合成，会返回相应错误

### 3. GET `/health`
检查 TTS 服务健康状态。

**响应：**
```json
{
    "status": "running"  // running, starting, error, stopped
}
```

**状态说明：**
- `running`: 所有实例正常运行
- `starting`: 部分实例正在初始化
- `error`: 所有实例出错
- `stopped`: 服务已停止

### 4. POST `/create_instance` (内部端点)
创建新的引擎实例（由 launcher 调用）。

**请求体：**
```json
{
    "config": {
        // 插件特定的完整配置参数
        // 用于初始化引擎实例
    }
}
```

**响应：**
```json
{
    "message": "Instance created successfully",
    "instance_id": "abc123def456",
    "config": { /* 返回的配置信息 */ }
}
```

### 5. DELETE `/instances/{instance_id}` (内部端点)
删除引擎实例（由 launcher 调用）。

**响应：**
```json
{
    "message": "Instance abc123def456 deleted successfully"
}
```

### 6. GET `/instances` (内部端点)
列出所有引擎实例（由 launcher 调用）。

**响应：**
```json
{
    "instances": {
        "abc123def456": {
            "ready": true,
            "supports_streaming": true,  // 是否支持流式合成
            "config": { /* 实例配置 */ }
        }
    }
}
```

### 7. GET `/plugin-config`
获取插件配置信息（用于远程插件发现）。

**响应：**
```json
{
    "name": "plugin_name",
    "version": "1.0.0",
    "description": "插件描述",
    "service_type": "tts",
    "plugin_json_schema": { /* JSON Schema 定义 */ },
    "plugin_ui_schema": { /* UI Schema 定义 */ }
}
```

## 参数说明

### plugin_config vs custom

#### plugin_config
- **用途**: 用于创建和标识引擎实例
- **特点**: 参与实例路由，相同配置会复用实例
- **内容**: 包含需要长时间加载的配置，如 API 密钥、模型选择、语音参考等
- **生命周期**: 在实例创建时使用，实例存在期间保持不变

#### custom
- **用途**: 运行时参数覆盖
- **特点**: 不参与实例路由，不影响实例的创建和复用
- **内容**: 包含可以快速变更的参数，如语速、音量、延迟模式、温度等
- **生命周期**: 每次请求时使用，可以随请求变化

## 支持的音频格式

音频格式支持取决于具体插件实现，常见格式包括：
- **MP3**: 压缩格式，适合网络传输
- **WAV**: 无损格式，质量最高
- **PCM**: 原始音频数据
- **Opus**: 低延迟格式，适合流式应用

## 延迟模式

延迟模式取决于具体插件实现，常见模式包括：
- **normal**: 标准延迟，更高的稳定性
- **balanced**: 平衡模式，在延迟和稳定性之间取平衡
- **fast**: 快速模式，最低延迟但可能降低稳定性

## 错误码

- **400**: 请求参数错误
  - 缺少必需字段 `text` 或 `plugin_config`
  - 文本内容格式无效
- **404**: 实例不存在
  - 指定的 `plugin_config` 对应的实例未找到
- **500**: 服务器内部错误
  - 合成过程中发生错误
  - 引擎初始化失败
- **503**: 服务不可用
  - 引擎实例未就绪

## 使用流程

### 1. 启动插件
通过 Launcher API 启动 TTS 插件：
```bash
curl -X POST http://127.0.0.1:7000/plugins/{plugin_name}/start
```

### 2. 创建实例
通过 Launcher API 创建插件实例：
```bash
curl -X POST http://127.0.0.1:7000/plugins/{plugin_name}/instances \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      // 插件特定的配置参数
    }
  }'
```

### 3. 合成语音
使用插件实例进行语音合成：
```bash
curl -X POST http://127.0.0.1:8080/synthesize \
  -H "Content-Type: application/json" \
  -d '{
    "text": "要合成的文本",
    "plugin_config": {
      // 与创建实例时相同的配置
    },
    "custom": {
      // 可选的运行时参数
    }
  }'
```

## 实例管理

TTS 插件使用**最小超集匹配策略**来管理实例：

1. **智能复用**: 使用最小超集匹配算法选择最合适的现有实例
2. **配置灵活性**: 简化的 `plugin_config` 可以匹配更完整的实例配置
3. **自动清理**: 实例在删除时会自动清理资源
4. **状态检查**: 实例具有就绪状态检查机制
5. **流式支持**: 部分实例支持流式合成功能
6. **最优选择**: 优先选择配置最精简的匹配实例，提高资源利用率

## 错误处理示例

### 1. 实例不存在错误
```json
{
  "detail": "No matching instance found for the provided plugin_config"
}
```

### 2. 没有可用实例错误
```json
{
  "detail": "No engine instances available"
}
```

### 3. 文本字段缺失错误
```json
{
  "detail": "text field is required"
}
```

### 4. 配置字段缺失错误
```json
{
  "detail": "plugin_config field is required"
}
```

### 5. 实例未就绪错误
```json
{
  "detail": "TTS engine instance not ready"
}
```

### 6. 合成失败错误
```json
{
  "detail": "Synthesis failed: [具体错误信息]"
}
```

### 7. 流式合成不支持错误
```json
{
  "error": "TTS engine does not support streaming"
}
```

## 性能优化建议

1. **实例复用**: 对于相同配置，复用已存在的实例可避免重复初始化开销
2. **参数分离**: 将需要长时间加载的配置放在 `plugin_config` 中，将可变参数放在 `custom` 中
3. **流式合成**: 对于长文本，使用 WebSocket 流式合成可以获得更好的用户体验
4. **格式选择**: 根据使用场景选择合适的音频格式，平衡质量和传输效率

## 开发注意事项

1. **音频编码**: 所有音频数据都使用 Base64 编码传输
2. **实例生命周期**: 实例由 Launcher 管理，插件本身不直接创建或删除实例
3. **异步处理**: 合成过程是异步的，适合处理长文本
4. **WebSocket 管理**: 流式连接需要正确处理连接状态和异常
5. **配置设计**: 合理划分 `plugin_config` 和 `custom` 参数，优化实例复用率
6. **流式支持**: 不是所有 TTS 引擎都支持流式合成，需要在实现中明确标识
