# ASR Plugin API 结构

## 实例路由策略

### 最小超集匹配（Minimal Superset Matching）

ASR Plugin 使用**最小超集匹配策略**来选择最合适的引擎实例：

**匹配规则：**
1. **必要条件**：实例配置必须包含 `plugin_config` 中的所有字段，且值完全匹配
2. **允许额外字段**：实例配置可以包含 `plugin_config` 中没有的额外字段
3. **最优选择**：在所有匹配的实例中，选择配置字段数量最少的实例
4. **同等情况下**：如果有多个实例具有相同的最少字段数，选择第一个

## ASR Plugin 端点

ASR Plugin 提供以下端点：

### 1. POST `/transcribe`
转录音频数据，使用已存在的插件配置实例。

**请求体：**
```json
{
    "audio": "base64_encoded_audio_data",  // Base64 编码的音频数据
    "plugin_config": {
        // 插件特定的配置参数，用于标识和创建实例
        // 相同的 plugin_config 会复用同一个实例
        // 通常包含模型路径、引擎类型等需要长时间加载的配置
    },
    "custom": {
        // 可选的运行时参数，不参与实例路由
        // 用于覆盖默认设置，如语言、采样率等
        // 这些参数不会影响实例的创建和复用
    }
}
```

**响应：**
```json
{
    "text": "转录的文本结果",
    "processing_time": 0.123,
    "instance_id": "abc123def456"  // 使用的实例ID（配置哈希）
}
```

**错误处理：**
- 如果 `plugin_config` 对应的实例不存在，返回 404 错误
- 不会自动创建新实例

### 2. GET `/health`
检查 ASR 服务健康状态。

**响应：**
```json
{
    "status": "running"  // running, starting, error, stopped
}
```

**状态说明：**
- `running`: 所有实例正常运行
- `starting`: 部分实例正在初始化
- `error`: 所有实例出错
- `stopped`: 服务已停止

### 3. POST `/create_instance` (内部端点)
创建新的引擎实例（由 launcher 调用）。

**请求体：**
```json
{
    "config": {
        // 插件特定的完整配置参数
        // 用于初始化模型和引擎实例
    }
}
```

**响应：**
```json
{
    "message": "Instance created successfully",
    "instance_id": "abc123def456",
    "config": { /* 返回的配置信息 */ }
}
```

### 4. DELETE `/delete_instance/{instance_id}` (内部端点)
删除引擎实例（由 launcher 调用）。

**响应：**
```json
{
    "message": "Instance 'abc123def456' deleted successfully",
    "instance_id": "abc123def456"
}
```

### 5. GET `/list_instances` (内部端点)
列出所有引擎实例（由 launcher 调用）。

**响应：**
```json
{
    "total_instances": 2,
    "instances": {
        "abc123def456": {
            "instance_id": "abc123def456",
            "ready": true
        },
        "def789ghi012": {
            "instance_id": "def789ghi012",
            "ready": false
        }
    }
}
```

### 6. GET `/plugin-config`
获取插件配置信息（用于远程插件发现）。

**响应：**
```json
{
    "name": "plugin_name",
    "version": "1.0.0",
    "description": "插件描述",
    "service_type": "asr",
    "plugin_json_schema": { /* JSON Schema 定义 */ },
    "plugin_ui_schema": { /* UI Schema 定义 */ }
}
```

## 参数说明

### plugin_config vs custom

#### plugin_config
- **用途**: 用于创建和标识引擎实例
- **特点**: 参与实例路由，相同配置会复用实例
- **内容**: 包含需要长时间加载的配置，如模型路径、引擎类型、硬件设置等
- **生命周期**: 在实例创建时使用，实例存在期间保持不变

#### custom
- **用途**: 运行时参数覆盖
- **特点**: 不参与实例路由，不影响实例的创建和复用
- **内容**: 包含可以快速变更的参数，如语言设置、采样率、解码参数等
- **生命周期**: 每次请求时使用，可以随请求变化

## 音频格式要求

- **编码格式**: Base64 编码的音频数据
- **采样率**: 通常为 16kHz，具体要求取决于插件
- **位深度**: 通常为 16-bit PCM
- **声道数**: 通常为单声道
- **数据格式**: 小端序（Little Endian）

## 错误码

- **400**: 请求参数错误
  - 缺少必需字段 `audio` 或 `plugin_config`
  - 音频数据格式无效
- **404**: 实例不存在
  - 指定的 `plugin_config` 对应的实例未找到
- **500**: 服务器内部错误
  - 转录过程中发生错误
  - 引擎初始化失败
- **503**: 服务不可用
  - 引擎实例未就绪

## 使用流程

### 1. 启动插件
通过 Launcher API 启动 ASR 插件：
```bash
curl -X POST http://127.0.0.1:7000/plugins/{plugin_name}/start
```

### 2. 创建实例
通过 Launcher API 创建插件实例：
```bash
curl -X POST http://127.0.0.1:7000/plugins/{plugin_name}/instances \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      // 插件特定的配置参数
    }
  }'
```

### 3. 转录音频
使用插件实例进行音频转录：
```bash
curl -X POST http://127.0.0.1:8080/transcribe \
  -H "Content-Type: application/json" \
  -d '{
    "audio": "base64_encoded_audio_data",
    "plugin_config": {
      // 与创建实例时相同的配置
    },
    "custom": {
      // 可选的运行时参数
    }
  }'
```

## 实例管理

ASR 插件使用**最小超集匹配策略**来管理实例：

1. **智能复用**: 使用最小超集匹配算法选择最合适的现有实例
2. **配置灵活性**: 简化的 `plugin_config` 可以匹配更完整的实例配置
3. **自动清理**: 实例在删除时会自动清理资源
4. **状态检查**: 实例具有就绪状态检查机制
5. **最优选择**: 优先选择配置最精简的匹配实例，提高资源利用率

## 错误处理示例

### 1. 实例不存在错误
```json
{
  "detail": "No matching instance found for the provided plugin_config"
}
```

### 2. 没有可用实例错误
```json
{
  "detail": "No engine instances available"
}
```

### 3. 音频字段缺失错误
```json
{
  "detail": "audio field is required"
}
```

### 4. 配置字段缺失错误
```json
{
  "detail": "plugin_config field is required"
}
```

### 5. 实例未就绪错误
```json
{
  "detail": "ASR engine instance not ready"
}
```

### 6. 转录失败错误
```json
{
  "detail": "Transcription failed: [具体错误信息]"
}
```

## 性能优化建议

1. **实例复用**: 对于相同配置，复用已存在的实例可避免重复初始化开销
2. **参数分离**: 将需要长时间加载的配置放在 `plugin_config` 中，将可变参数放在 `custom` 中
3. **批处理**: 对于大量音频文件，考虑使用相同实例进行批量处理
4. **音频预处理**: 确保音频格式符合要求，避免格式转换开销

## 开发注意事项

1. **音频编码**: 所有音频数据都必须使用 Base64 编码传输
2. **实例生命周期**: 实例由 Launcher 管理，插件本身不直接创建或删除实例
3. **异步处理**: 转录过程是异步的，适合处理大文件
4. **资源管理**: 实例会自动管理模型资源的加载和释放
5. **配置设计**: 合理划分 `plugin_config` 和 `custom` 参数，优化实例复用率