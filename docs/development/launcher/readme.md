# OLV Launcher 概述

OLV Launcher 是 OLV（Open Loop Voice）平台的中央插件和服务管理系统。它负责发现、管理和协调各种服务类型（ASR、TTS、LLM）的插件。Launcher 提供统一的 REST API 接口，用于管理插件的生命周期、健康状态、端口分配，以及提供 JSON Schema 和 UI Schema 的统一访问。

## 架构概述

OLV Launcher 采用模块化设计，基于 FastAPI 框架实现 RESTful API 服务。其核心组件包括插件管理器、端口管理器、路由模块和 schema 管理系统。每个插件可以提供 JSON Schema（用于数据验证）和 UI Schema（用于前端表单生成）。

```mermaid
graph TD
    Client[客户端] -->|HTTP 请求| FastAPI[FastAPI 应用]
    FastAPI -->|依赖注入| PM[插件管理器]
    PM -->|管理| Plugins[插件]
    PM -->|使用| PortM[端口管理器]
    PortM -->|分配| Ports[端口资源]
    PM -->|加载| Schemas[Schema 文件]
    
    subgraph Routers[路由模块]
        R1[插件路由]
        R2[服务路由]
        R3[端口路由]
        R4[Schema 路由]
    end
    
    FastAPI -->|路由| Routers
    
    subgraph Services[插件服务类型]
        S1[ASR 服务]
        S2[TTS 服务]
        S3[LLM 服务]
    end
    
    subgraph SchemaTypes[Schema 类型]
        JS[JSON Schema]
        US[UI Schema]
        ET[Engine 配置]
        CT[Character 配置]
    end
    
    Plugins -->|类型| Services
    Schemas -->|类型| SchemaTypes
```

## 目录结构

```
src/olv_launcher/
├── __init__.py              # 模块初始化文件
├── server.py                # FastAPI 应用程序入口点
├── plugin_manager.py        # 插件管理器实现
├── port_mananger.py         # 端口管理器实现
├── dependencies.py          # 依赖注入函数
├── exceptions.py            # 自定义异常类
├── middleware.py            # 中间件实现
├── models/                  # Pydantic 模型包
│   ├── __init__.py          # 模型包初始化
│   ├── api.py               # API 模型定义
│   ├── config.py            # 配置模型定义
│   ├── service.py           # 服务模型定义
│   ├── port.py              # 端口相关模型
│   └── plugin.py            # 插件相关模型
└── routers/                 # 路由模块
    ├── __init__.py          # 路由包初始化
    ├── plugins.py           # 插件管理路由
    ├── services.py          # 服务管理路由
    └── ports.py             # 端口管理路由
```

## 文档导航

- [部署指南 (Deployment)](./deployment.md) - 开发环境部署
- [插件开发指南 (Plugin Development)](../plugins/readme.md) - 如何开发插件
- [配置管理 (Configuration)](./configuration.md) - 环境变量和配置选项
- [插件管理器 (Plugin Manager)](./plugin-manager.md) - 插件生命周期管理
- [端口管理器 (Port Manager)](./port-manager.md) - 端口分配和管理
- [数据模型 (Models)](./models.md) - Pydantic 数据模型
- [API 参考 (API Reference)](./api-reference.md) - 完整的 `openapi.json`
