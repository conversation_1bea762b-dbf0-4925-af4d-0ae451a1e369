# 端口管理器 (PortManager)

OLV Launcher 中的 `PortManager` 负责为插件服务动态分配和管理网络端口。这确保了每个插件服务都能在唯一的端口上运行，避免冲突。

## 核心职责

- **端口范围管理**: 解析和管理一个或多个预定义的端口范围。
- **端口分配**: 为请求服务的插件从可用端口池中分配一个唯一的端口。
- **端口释放**: 回收已停止服务的插件所占用的端口，使其可供其他插件使用。
- **状态查询**: 提供查询当前端口分配状态（已分配端口、可用端口、总范围）的功能。

## 初始化

`PortManager` 在实例化时需要一个端口范围列表。这些范围定义了管理器可以从中分配端口的池。

```python
class PortManager:
    def __init__(self, port_ranges: List[str]):
        """
        Initialize port manager with configurable port ranges.

        Args:
            port_ranges: List of port range strings like ["8001-8005", "12000-12010"]
        """
        # ...
```

端口范围字符串列表由 `LauncherSettings` 中的 `OLV_LAUNCHER_DEFAULT_PORT_RANGES` 配置项提供，其默认值为 `["8001-8020", "9001-9020", "10001-10020"]`。详见 [配置管理](./configuration.md)。

## 工作流程

```mermaid
flowchart TD
    A[初始化 PortManager] --> B[解析端口范围字符串]
    B --> C{范围格式有效?}
    C -->|否| D[抛出 PortAllocationError]
    C -->|是| E[转换为 PortRange 对象]
    E --> F[构建可用端口集合]
    
    G[插件请求端口] --> H{插件已分配端口?}
    H -->|是| I[返回已分配端口]
    H -->|否| J[查找最小可用端口]
    J --> K{找到可用端口?}
    K -->|否| L[抛出 PortAllocationError]
    K -->|是| M[记录到 allocated_ports]
    M --> N[返回分配的端口]
    
    O[插件停止服务] --> P[释放端口]
    P --> Q[从 allocated_ports 移除]
    Q --> R[端口重新可用]
```

### 1. 解析端口范围 (`_parse_port_ranges`)

- 初始化时，`PortManager` 会解析传入的字符串格式的端口范围。
- 每个范围可以是单个端口 (如 `"8080"`) 或一个连字符连接的起止端口 (如 `"8001-8005"`)。
- 解析后的范围将转换为 `PortRange` 对象（定义见 [数据模型](./models.md#portrange-portpy)）并存储。
- 如果任何范围字符串格式无效（例如，起始端口大于结束端口，或非法的端口号），则会抛出 `PortAllocationError`。

### 2. 构建可用端口集 (`_build_available_ports`)

- 解析完所有端口范围后，`PortManager` 会将这些范围内的所有端口号聚合起来，形成一个初始的可用端口集合 (`available_ports`)。

### 3. 分配端口 (`allocate_port`)

- 当插件需要启动时，会调用 `allocate_port(plugin_name)` 方法。
- 如果该 `plugin_name` 已经分配过端口，则直接返回已分配的端口。
- 否则，`PortManager` 会从 `available_ports` 集合中查找一个当前未被任何其他插件占用的最小可用端口。
- 找到可用端口后，会将其记录在 `allocated_ports` 字典中（键为 `plugin_name`，值为端口号），并从 `available_ports` 逻辑上移除（通过检查 `allocated_ports.values()` 来判断是否可用）。
- 如果在所有已定义的端口范围内都找不到可用端口，则会抛出 `PortAllocationError`。

### 4. 释放端口 (`release_port`)

- 当插件停止服务时，会调用 `release_port(plugin_name)` 方法。
- `PortManager` 会从 `allocated_ports` 字典中移除该插件的条目。
- 该端口随后即可被其他插件分配和使用。

## 主要接口方法

下表总结了 `PortManager` 提供的主要公共方法：

| 方法名                | 描述                                                                 | 返回类型                  |
|-----------------------|----------------------------------------------------------------------|---------------------------|
| `allocate_port(plugin_name)` | 为指定插件分配一个端口。如果已分配，则返回现有端口。如果无可用端口，则抛出 `PortAllocationError`。 | `int`                     |
| `release_port(plugin_name)`  | 释放指定插件占用的端口。                                                   | `None`                    |
| `get_port(plugin_name)`      | 获取指定插件当前分配的端口号。如果未分配，则返回 `None`。                             | `Optional[int]`           |
| `get_status()`               | 获取当前端口分配的详细状态，包括已分配端口、可用端口列表和总端口范围。                         | `Dict`                    |

## 依赖与数据模型

- `PortManager` 依赖于 `PortRange` 数据模型来表示和操作端口范围。详情请参阅 [数据模型文档](./models.md#portrange-portpy)。
- 端口分配失败时，会抛出 `PortAllocationError` 异常。该异常定义在 `olv_launcher.exceptions` 模块中。
- 端口范围的配置通过 `LauncherSettings` 进行管理，具体配置项为 `OLV_LAUNCHER_DEFAULT_PORT_RANGES`，详情请参阅 [配置管理文档](./configuration.md)。
