# 部署指南 (Deployment)

本指南介绍如何部署和运行 OLV Launcher 服务。OLV Launcher 使用 `uv` 作为 Python 包管理工具，提供便捷的依赖管理和启动方式。

## 环境要求

## 安装与设置

### 1. 安装依赖

使用 `uv` 同步项目依赖：

```bash
uv sync
```

### 2. 环境配置

参考 [配置管理](./configuration.md) 修改 `.env` 文件配置 Launcher 设置（可选）。

## 启动服务

```bash
uv run run_launcher
```

## 服务验证
访问交互式 API 文档：

- **Swagger UI**: http://localhost:7000/docs
- **ReDoc**: http://localhost:7000/redoc

在 Swagger UI 中，你可以：

- 查看所有可用的 API 端点
- 测试插件管理功能
- 查看插件发现和启动状态
- 监控端口分配情况
- 实时查看插件日志