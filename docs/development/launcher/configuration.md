# OLV Launcher 配置管理

OLV Launcher 使用基于 Pydantic BaseSettings 的配置管理系统，支持环境变量、.env 文件和默认值的多层配置加载机制。

## 配置加载优先级

配置系统按以下优先级加载设置：

1. **环境变量** - 最高优先级，以 `OLV_LAUNCHER_` 为前缀
2. **.env 文件** - 项目根目录下的 .env 文件
3. **默认值** - 代码中定义的默认配置

## 配置文件设置

### 创建配置文件

1. 复制示例配置文件：
```bash
cp .env.example .env
cp olv-launcher-ui/.env.example olv-launcher-ui/.env
```

2. 根据需要修改 `.env` 文件中的配置项

## Launcher Server

控制 OLV Launcher 服务器的基本运行参数。

| 配置项 | 环境变量 | 类型 | 默认值 | 描述 |
|--------|----------|------|--------|------|
| `host` | `OLV_LAUNCHER_HOST` | string | `127.0.0.1` | 服务器监听地址 |
| `port` | `OLV_LAUNCHER_PORT` | int | `7000` | 服务器端口号 (1024-65535) |
| `reload` | `OLV_LAUNCHER_RELOAD` | bool | `false` | 开发模式自动重载 |

**示例配置：**
```bash
# 允许外部访问
OLV_LAUNCHER_HOST=0.0.0.0
OLV_LAUNCHER_PORT=8080

# 开发环境启用自动重载
OLV_LAUNCHER_RELOAD=true
```

### 插件配置

管理插件发现、加载和端口分配的相关设置。

| 配置项 | 环境变量 | 类型 | 默认值 | 描述 |
|--------|----------|------|--------|------|
| `plugins_dir` | `OLV_LAUNCHER_PLUGINS_DIR` | string | `plugins` | 插件目录路径 |
| `default_port_ranges` | `OLV_LAUNCHER_DEFAULT_PORT_RANGES` | list | `["8001-8020", "9001-9020", "10001-10020"]` | 端口分配范围 |

**示例配置：**
```bash
# 自定义插件目录
OLV_LAUNCHER_PLUGINS_DIR=/opt/olv/plugins

# 扩展端口范围
OLV_LAUNCHER_DEFAULT_PORT_RANGES=8001-8050,9001-9050,10001-10050
```

### 服务管理配置

控制插件生命周期管理和健康检查的时间参数。

| 配置项 | 环境变量 | 类型 | 默认值 | 最小值 | 描述 |
|--------|----------|------|--------|--------|------|
| `health_check_interval` | `OLV_LAUNCHER_HEALTH_CHECK_INTERVAL` | int | `30` | `5` | 健康检查间隔（秒） |
| `plugin_startup_timeout` | `OLV_LAUNCHER_PLUGIN_STARTUP_TIMEOUT` | int | `600` | `30` | 插件启动超时（秒） |
| `plugin_health_timeout` | `OLV_LAUNCHER_PLUGIN_HEALTH_TIMEOUT` | int | `10` | `1` | 健康检查超时（秒） |

**示例配置：**
```bash
# 更频繁的健康检查
OLV_LAUNCHER_HEALTH_CHECK_INTERVAL=15

# 延长启动超时（适用于大型模型）
OLV_LAUNCHER_PLUGIN_STARTUP_TIMEOUT=1200
```

### 日志配置

控制系统日志输出的级别和格式。

| 配置项 | 环境变量 | 类型 | 默认值 | 可选值 | 描述 |
|--------|----------|------|--------|--------|------|
| `log_level` | `OLV_LAUNCHER_LOG_LEVEL` | string | `INFO` | `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL` | 日志级别 |
| `log_format` | `OLV_LAUNCHER_LOG_FORMAT` | string | `%(asctime)s - %(name)s - %(levelname)s - %(message)s` | Python logging 格式 | 日志格式字符串 |

**示例配置：**
```bash
# 开发环境使用详细日志
OLV_LAUNCHER_LOG_LEVEL=DEBUG

# 自定义日志格式
OLV_LAUNCHER_LOG_FORMAT=[%(levelname)s] %(asctime)s - %(name)s: %(message)s
```

### CORS 配置

配置跨域资源共享（CORS）策略，用于前端应用访问。

| 配置项 | 环境变量 | 类型 | 默认值 | 描述 |
|--------|----------|------|--------|------|
| `cors_origins` | `OLV_LAUNCHER_CORS_ORIGINS` | list | `["*"]` | 允许的源地址 |
| `cors_credentials` | `OLV_LAUNCHER_CORS_CREDENTIALS` | bool | `true` | 允许携带凭据 |
| `cors_methods` | `OLV_LAUNCHER_CORS_METHODS` | list | `["*"]` | 允许的HTTP方法 |
| `cors_headers` | `OLV_LAUNCHER_CORS_HEADERS` | list | `["*"]` | 允许的请求头 |

**示例配置：**
```bash
# 限制特定域名访问
OLV_LAUNCHER_CORS_ORIGINS=http://localhost:3000,https://myapp.com

# 限制HTTP方法
OLV_LAUNCHER_CORS_METHODS=GET,POST,PUT,DELETE

# 自定义允许的头部
OLV_LAUNCHER_CORS_HEADERS=Content-Type,Authorization,X-Requested-With
```

### 环境同步配置

控制 Python 环境依赖的自动同步功能。

| 配置项 | 环境变量 | 类型 | 默认值 | 最小值 | 描述 |
|--------|----------|------|--------|--------|------|
| `enable_uv_sync` | `OLV_LAUNCHER_ENABLE_UV_SYNC` | bool | `true` | - | 启用UV环境同步 |
| `uv_sync_timeout` | `OLV_LAUNCHER_UV_SYNC_TIMEOUT` | int | `300` | `60` | UV同步超时（秒） |

**示例配置：**
```bash
# 禁用自动环境同步
OLV_LAUNCHER_ENABLE_UV_SYNC=false

# 延长同步超时
OLV_LAUNCHER_UV_SYNC_TIMEOUT=600
```

## Launcher UI

OLV Launcher UI 使用独立的配置系统，通过环境变量进行配置。

| 配置项 | 环境变量 | 类型 | 默认值 | 描述 |
|--------|----------|------|--------|------|
| `apiBaseUrl` | `NEXT_PUBLIC_API_BASE_URL` | string | `http://localhost:7000` | 后端API基础地址 |
| `apiTimeout` | `NEXT_PUBLIC_API_TIMEOUT` | number | `600000` | API请求超时时间（毫秒） |
| `port` | `PORT` | number | `3000` | 前端开发服务器端口号 |
| `nodeEnv` | `NODE_ENV` | string | `development` | 运行环境模式 |

## 相关文档

- [部署指南](./deployment.md) - 详细的部署配置
- [插件管理器](./plugin-manager.md) - 插件配置和管理
- [API 参考](./api-reference.md) - 配置相关的API端点