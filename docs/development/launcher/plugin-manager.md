# 插件管理器 (Plugin Manager)

OLV Launcher 的插件管理器 (`UnifiedPluginManager`) 负责统一管理所有服务类型（ASR, TTS, LLM）的插件。它处理插件的发现、环境同步、生命周期控制、端口分配和日志记录。

## 核心职责

`UnifiedPluginManager` 的主要职责包括：

*   **插件发现**: 扫描指定目录（通常由 `settings.plugins_dir` 配置，默认为 `"plugins/"`），根据 `plugin.json` 和目录结构识别合法的插件。
*   **环境同步**: 若启用 (`settings.enable_uv_sync`)，自动使用 `uv sync` 命令同步插件的 Python 环境，确保依赖正确安装。同步超时由 `settings.uv_sync_timeout` 控制。
*   **生命周期管理**: 启动和停止插件服务进程（本地插件）或健康检查（远程插件）。
*   **端口管理**: 与端口管理器 (`PortManager`) 协作，为本地插件动态分配和释放运行所需的端口。
*   **日志收集**: 捕获本地插件进程的标准输出 (stdout) 和标准错误 (stderr) 流，并提供查阅接口。
*   **状态查询**: 提供查询插件运行状态、所用端口、已发现插件列表等信息的功能。
*   **Schema 管理**: 从插件配置中获取 JSON Schema 和 UI Schema，支持实例创建的表单配置。
*   **远程插件支持**: 管理远程插件的健康检查和状态监控。

## 插件类型

OLV Launcher 支持两种插件类型：

### 本地插件 (Local Plugins)
本地插件在 Launcher 进程内运行，由 Launcher 管理其生命周期。

### 远程插件 (Remote Plugins)  
远程插件是独立的外部服务，通过 HTTP API 与 Launcher 通信。

## 插件配置结构 (plugin.json)

基于真实代码中的配置结构，`plugin.json` 包含所有插件元数据和配置信息：

### ASR 插件示例（来自 sherpa_onnx_asr_cpu_plugin）

```json
{
  "name": "sherpa_onnx_asr_cpu_plugin",
  "version": "1.0.0",
  "description": "Sherpa-ONNX ASR Plugin supporting multiple model types",
  "author": "OLV Team",
  "service_type": "asr",
  "package_manager": "uv",
  "plugin_json_schema": {
    "type": "object",
    "title": "Sherpa-ONNX ASR Engine Configuration",
    "properties": {
      "model_type": {
        "type": "string",
        "title": "Model Type",
        "enum": ["transducer", "paraformer", "nemo_ctc", "wenet_ctc", "whisper", "tdnn_ctc", "sense_voice"]
      },
      "sense_voice": {
        "type": "string",
        "title": "SenseVoice Model"
      },
      "tokens": {
        "type": "string",
        "title": "Tokens File"
      },
      "provider": {
        "type": "string",
        "title": "ONNX Provider",
        "enum": ["cpu", "cuda"]
      },
      "num_threads": {
        "type": "integer",
        "title": "Number of Threads",
        "minimum": 1,
        "maximum": 32
      },
      "debug": {
        "type": "boolean",
        "title": "Debug Mode"
      },
      "use_itn": {
        "type": "boolean",
        "title": "Use ITN"
      }
    },
    "required": ["model_type", "tokens"],
    "default": {
      "model_type": "sense_voice",
      "sense_voice": "./models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17/model.onnx",
      "tokens": "./models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17/tokens.txt",
      "provider": "cpu",
      "num_threads": 4,
      "debug": false,
      "use_itn": true
    }
  },
  "plugin_ui_schema": {
    "ui:title": "Sherpa-ONNX ASR Engine Configuration",
    "ui:description": "Configure the Sherpa-ONNX ASR engine settings for speech recognition",
    "model_type": {
      "ui:widget": "select",
      "ui:title": "Model Type",
      "ui:description": "Select the type of ASR model to use.",
      "ui:help": "Different model types have different capabilities and performance characteristics."
    },
    "sense_voice": {
      "ui:widget": "textarea",
      "ui:title": "SenseVoice Model Path",
      "ui:description": "Path to the SenseVoice model file.",
      "ui:placeholder": "e.g., /path/to/sense_voice.onnx"
    },
    "tokens": {
      "ui:widget": "textarea",
      "ui:title": "Tokens File Path",
      "ui:description": "Path to the tokens file.",
      "ui:placeholder": "e.g., /path/to/tokens.txt"
    }
  }
}
```

### TTS 插件示例（来自 fish_audio_tts_plugin）

```json
{
  "name": "fish_audio_tts_plugin",
  "version": "1.0.0",
  "description": "Fish Audio TTS Plugin supporting both HTTP and WebSocket connections",
  "author": "OLV Team",
  "service_type": "tts",
  "package_manager": "uv",
  "plugin_json_schema": {
    "type": "object",
    "title": "Fish Audio TTS Engine Configuration",
    "properties": {
      "api_key": {
        "type": "string",
        "title": "API Key",
        "description": "Fish Audio API key"
      },
      "base_url": {
        "type": "string",
        "title": "Base URL",
        "default": "https://api.fish.audio"
      },
      "reference_id": {
        "type": "string",
        "title": "Reference ID",
        "description": "Voice reference ID from Fish Audio"
      },
      "model": {
        "type": "string",
        "title": "TTS Model",
        "enum": ["speech-1.5", "speech-1.6"],
        "default": "speech-1.6"
      },
      "format": {
        "type": "string",
        "title": "Audio Format",
        "enum": ["wav", "pcm", "mp3", "opus"],
        "default": "mp3"
      },
      "temperature": {
        "type": "number",
        "title": "Temperature",
        "minimum": 0.0,
        "maximum": 2.0,
        "default": 0.7,
        "description": "Controls randomness in speech generation"
      },
      "prosody": {
        "type": "object",
        "title": "Prosody Settings",
        "properties": {
          "speed": {
            "type": "number",
            "title": "Speech Speed",
            "minimum": 0.5,
            "maximum": 2.0,
            "default": 1.0
          },
          "volume": {
            "type": "number",
            "title": "Volume Adjustment (dB)",
            "default": 0
          }
        }
      }
    },
    "required": ["api_key"],
    "default": {
      "api_key": "",
      "base_url": "https://api.fish.audio",
      "reference_id": "",
      "model": "speech-1.6",
      "format": "mp3",
      "temperature": 0.7,
      "prosody": {
        "speed": 1.0,
        "volume": 0
      }
    }
  },
  "plugin_ui_schema": {
    "ui:title": "Fish Audio TTS Configuration",
    "ui:description": "Configure Fish Audio TTS plugin settings",
    "api_key": {
      "ui:widget": "password",
      "ui:placeholder": "Enter your Fish Audio API key"
    },
    "reference_id": {
      "ui:placeholder": "Enter voice reference ID"
    },
    "prosody": {
      "ui:title": "Prosody Control",
      "speed": {
        "ui:widget": "range"
      },
      "volume": {
        "ui:widget": "updown"
      }
    }
  }
}
```

### 远程插件示例

远程插件需要在 `plugin.json` 中指定 `service_url`：

```json
{
  "name": "remote_asr_service",
  "version": "1.0.0",
  "description": "Remote ASR Service",
  "author": "External Provider",
  "service_type": "asr",
  "service_url": "http://remote-asr.example.com:8080",
  "plugin_json_schema": {
    "type": "object",
    "title": "Remote ASR Configuration",
    "properties": {
      "model_name": {
        "type": "string",
        "enum": ["whisper-small", "whisper-large"]
      },
      "language": {
        "type": "string",
        "default": "auto"
      }
    },
    "required": ["model_name"],
    "default": {
      "model_name": "whisper-small",
      "language": "auto"
    }
  }
}
```

### 关键字段说明

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `name` | string | 是 | 插件名称，必须与目录名一致 |
| `version` | string | 是 | 插件版本号 |
| `service_type` | string | 是 | 服务类型 (asr, tts, llm) |
| `service_url` | string | 否 | 远程插件的服务地址，本地插件可省略 |
| `package_manager` | string | 否 | 包管理器类型，默认为 "uv" |
| `plugin_json_schema` | object | 否 | JSON Schema 定义，包含配置属性和默认值 |
| `plugin_ui_schema` | object | 否 | UI Schema 定义，控制表单渲染 |

## 初始化

`UnifiedPluginManager` 在应用程序启动时被实例化，配置参数来源于 `LauncherSettings`。管理器通过构造函数接收插件目录路径和端口范围配置。

| 参数 | 描述 | 来源 | 默认值 |
|------|------|------|--------|
| `plugins_dir` | 存放插件的根目录路径 | `settings.plugins_dir` | `"plugins"` |
| `port_ranges` | 用于插件端口分配的端口范围列表 | `settings.default_port_ranges` | `["8001-8020", "9001-9020", "10001-10020"]` |

## 工作流程

```mermaid
flowchart TD
    A[启动 UnifiedPluginManager] --> B[扫描 plugins_dir]
    B --> C[遍历服务类型目录<br/>ASR, TTS, LLM]
    C --> D[检查插件目录]
    D --> E{包含 plugin.json?}
    E -->|否| F[跳过该目录]
    E -->|是| G[解析 plugin.json]
    G --> H{service_type 匹配目录?}
    H -->|否| I[记录警告]
    H -->|是| J{是远程插件?}
    J -->|是| K[存储远程插件信息]
    J -->|否| L{包含 pyproject.toml?}
    L -->|否| M[记录警告，跳过]
    L -->|是| N{启用 UV 同步?}
    N -->|是| O[执行 uv sync]
    N -->|否| P[存储本地插件信息]
    O --> Q{同步成功?}
    Q -->|是| P
    Q -->|否| R[记录错误但继续]
    R --> P
    
    F --> S{还有目录?}
    I --> S
    K --> S
    M --> S
    P --> S
    S -->|是| D
    S -->|否| T[插件发现完成]
    
    U[启动插件请求] --> V{插件存在?}
    V -->|否| W[返回错误]
    V -->|是| X{是远程插件?}
    X -->|是| Y[检查远程服务健康状态]
    X -->|否| Z{插件已运行?}
    Z -->|是| AA[返回现有端口]
    Z -->|否| BB[分配端口]
    BB --> CC{端口分配成功?}
    CC -->|否| DD[抛出 ServiceError]
    CC -->|是| EE[构建启动命令]
    EE --> FF[启动进程]
    FF --> GG[启动日志捕获线程]
    GG --> HH[健康检查]
    HH --> II{服务就绪?}
    II -->|否| JJ{超时?}
    JJ -->|是| KK[清理资源并抛出错误]
    JJ -->|否| HH
    II -->|是| LL[记录运行状态]
    Y --> MM{健康检查通过?}
    MM -->|是| LL
    MM -->|否| NN[返回错误]
```

### 1. 插件发现

插件发现过程扫描 `plugins_dir` 下的结构化目录。管理器遍历服务类型目录（如 asr、tts、llm），在每个服务类型目录中查找插件子目录。

发现过程包括：
- 验证目录结构（`plugins/service_type/plugin_name/`）
- 解析 `plugin.json` 配置文件
- 检查 `service_type` 与目录层级匹配
- 区分本地和远程插件
- 对本地插件检查 `pyproject.toml` 存在性

### 2. 环境同步

对本地插件进行环境同步。如果全局配置启用了 UV 同步，管理器会在插件目录下执行 `uv sync` 命令来安装或更新依赖。同步操作有超时限制，失败时会记录错误但不阻止其他操作。

### 3. 启动插件服务

插件启动流程根据插件类型有所不同：

#### 本地插件启动

本地插件启动过程包括：
1. 端口分配 - 从可用端口池中分配唯一端口
2. 构建启动命令 - 使用 UV 和 uvicorn 构建标准化启动命令
3. 进程启动 - 使用 subprocess 启动插件进程
4. 日志捕获 - 启动独立线程读取进程输出
5. 健康检查 - 轮询插件的 `/health` 端点等待服务就绪

实际启动命令结构：
```bash
uv run --project <plugin_dir> python -m uvicorn <plugin_name>.server:app --host 127.0.0.1 --port <port> --log-level info --access-log --no-use-colors
```

#### 远程插件启动

远程插件启动过程相对简单：
1. 设置状态为 STARTING
2. 向远程服务的 `/health` 端点发送 HTTP 请求
3. 检查响应状态，确认服务可用性
4. 更新插件状态为 RUNNING 或错误状态

### 4. 停止插件服务

停止插件服务的处理也区分插件类型：

- **远程插件**: 仅更新内部状态为 STOPPED，不进行实际停止操作
- **本地插件**: 执行完整的停止流程，包括发送终止信号、等待进程退出、释放端口、清理状态记录

### 5. 日志管理

本地插件的日志管理功能包括：
- 为每个运行的插件启动独立的日志读取线程
- 分别处理 stdout 和 stderr 流
- 日志格式化（添加时间戳和插件名）
- 内存存储最近的1000条日志记录
- 同时输出到主系统日志

## 主要接口方法

基于实际代码实现，主要公共方法包括：

| 方法名 | 描述 |
|--------|------|
| `discover_plugins()` | 发现 `plugins_dir` 中的所有可用插件并加载 Schema |
| `start_plugin_service(plugin_name)` | 异步启动指定插件服务，返回服务 URL |
| `stop_plugin_service(plugin_name)` | 停止指定插件服务 |
| `stop_all_services()` | 停止所有当前正在运行的插件服务 |
| `get_plugins_by_service(service_type)` | 获取指定服务类型的所有插件列表 |
| `get_all_plugins()` | 获取所有已发现插件的列表 |
| `get_plugin_schemas(plugin_name)` | 获取指定插件的配置 Schema |
| `get_plugin_port(plugin_name)` | 获取正在运行的指定插件所使用的端口号 |
| `is_plugin_running(plugin_name)` | 检查指定的插件服务当前是否正在运行 |
| `get_port_status()` | 获取端口分配状态 |
| `configure_ports(port_ranges)` | 重新配置端口范围 |
| `get_plugin_logs(plugin_name, lines)` | 获取指定插件最近的日志 |
| `clear_plugin_logs(plugin_name)` | 清除指定插件存储的日志记录 |
| `create_plugin_instance(plugin_name, config)` | 为指定插件创建新的配置实例 |
| `list_plugin_instances(plugin_name)` | 列出指定插件的所有实例 |
| `delete_plugin_instance(plugin_name, instance_id)` | 删除指定插件的特定实例 |
| `force_cleanup_plugin(plugin_name)` | 强制清理插件状态（处理卡死情况）|

## 依赖与配置

`UnifiedPluginManager` 依赖的 `LauncherSettings` 配置项涵盖插件目录、端口范围、超时设置和环境同步选项。

### 环境变量配置

| 环境变量 | 默认值 | 描述 |
|----------|--------|------|
| `OLV_LAUNCHER_PLUGINS_DIR` | `"plugins"` | 插件根目录 |
| `OLV_LAUNCHER_DEFAULT_PORT_RANGES` | `["8001-8020", "9001-9020", "10001-10020"]` | 端口范围列表 |
| `OLV_LAUNCHER_PLUGIN_STARTUP_TIMEOUT` | `600` | 插件启动超时时间（秒）|
| `OLV_LAUNCHER_PLUGIN_HEALTH_TIMEOUT` | `10` | 健康检查超时时间（秒）|
| `OLV_LAUNCHER_ENABLE_UV_SYNC` | `True` | 是否启用 UV 环境同步 |
| `OLV_LAUNCHER_UV_SYNC_TIMEOUT` | `300` | UV 同步超时时间（秒）|
