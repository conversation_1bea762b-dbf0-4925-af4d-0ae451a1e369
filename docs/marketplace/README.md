# OLV 插件市场系统

OLV 插件市场系统是一个功能完整的插件发现、安装和管理平台，支持从 GitHub 仓库自动获取插件信息，提供一键安装、更新和卸载功能。

## 功能特性

### 🔍 插件发现与浏览
- **自动发现**: 从配置的 GitHub 仓库自动获取插件列表
- **分类浏览**: 按服务类型（ASR、TTS、LLM）分类显示
- **搜索过滤**: 支持关键词搜索、作者筛选、标签过滤
- **排序功能**: 按名称、评分、下载量、更新时间排序

### 📦 插件管理
- **一键安装**: 直接从市场安装插件到本地
- **版本管理**: 自动检测版本更新，支持一键更新
- **依赖处理**: 自动安装插件依赖
- **安全卸载**: 完整清理插件文件和配置

### 🔄 更新系统
- **批量检查**: 一键检查所有插件更新
- **单独更新**: 针对特定插件的更新操作
- **版本对比**: 清晰显示当前版本和最新版本
- **更新日志**: 显示版本更新内容

### 📊 状态管理
- **实时状态**: 显示插件安装、更新、卸载状态
- **进度跟踪**: 详细的安装进度显示
- **错误处理**: 完善的错误信息和恢复机制

## 系统架构

## 配置说明

### 后端配置

可以通过环境变量覆盖默认配置：

```bash
# 插件市场 GitHub 仓库 URL
OLV_LAUNCHER_PLUGIN_MARKETPLACE_URL=https://github.com/your-org/your-plugins

# 使用的分支
OLV_LAUNCHER_PLUGIN_MARKETPLACE_BRANCH=main

# 缓存时间（秒）
OLV_LAUNCHER_PLUGIN_MARKETPLACE_CACHE_TTL=300

# 下载超时时间（秒）
OLV_LAUNCHER_PLUGIN_DOWNLOAD_TIMEOUT=600
```

## API 接口

### 获取插件列表
```http
GET /marketplace/plugins?query=&service_type=&sort_by=name&sort_order=asc
```

### 刷新市场数据
```http
POST /marketplace/refresh?force=false
```

### 安装插件
```http
POST /marketplace/install
Content-Type: application/json

{
  "plugin_name": "plugin_name"
}
```

### 获取安装进度
```http
GET /marketplace/install/{plugin_name}/progress
```

### 卸载插件
```http
POST /marketplace/uninstall
Content-Type: application/json

{
  "plugin_name": "plugin_name"
}
```

### 检查更新
```http
GET /marketplace/updates?plugin_names=plugin1&plugin_names=plugin2
```

### 更新插件
```http
POST /marketplace/update
Content-Type: application/json

{
  "plugin_name": "plugin_name"
}
```
