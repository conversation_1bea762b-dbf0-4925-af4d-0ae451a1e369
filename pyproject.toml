[project]
name = "open-llm-vtuber"
version = "1.0.0"
description = ""
readme = "README.md"
requires-python = ">=3.10,<3.13"
dependencies = [
    "fastapi>=0.115.12",
    "httpx>=0.28.1",
    "pydantic>=2.11.5",
    "pydantic-settings>=2.9.1",
    "pytest>=8.3.5",
    "pytest-asyncio>=1.0.0",
    "uvicorn>=0.34.2",
]

[project.scripts]
l = "src.olv_launcher.server:main"

[tool.uv]
package = true
dev-dependencies = []